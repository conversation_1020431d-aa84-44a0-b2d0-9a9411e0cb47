{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "osdebug",
            "type": "cppdbg",
            "request": "launch",
            "program": "./target/riscv64gc-unknown-none-elf/debug/kernel",
            "stopAtEntry": true,
            "cwd": "${workspaceFolder}",
            "miDebuggerServerAddress": "127.0.0.1:1234", //见.gdbinit 中 target remote xxxx:xx
            "miDebuggerPath": "/usr/bin/gdb-multiarch", // which gdb-multiarch
            "MIMode": "gdb",
        }
    ]
}
