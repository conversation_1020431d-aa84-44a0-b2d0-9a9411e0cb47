{"version": "2.0.0", "tasks": [{"label": "build-kernel", "type": "shell", "command": "make", "args": ["build"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "run-qemu-debug", "type": "shell", "command": "make", "args": ["qemu-riscv64-debug"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": [{"pattern": [{"regexp": ".", "file": 1, "location": 2, "message": 3}], "background": {"activeOnStart": true, "beginsPattern": "Running RISC-V64 kernel in QEMU with GDB server", "endsPattern": "Waiting for gdb connection on port 1234"}}], "options": {"cwd": "${workspaceFolder}"}}, {"label": "clean", "type": "shell", "command": "make", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}}]}