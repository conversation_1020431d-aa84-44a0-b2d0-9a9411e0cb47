# Echos Kernel Makefile
# 支持 RISC-V64 和 LoongArch64 架构

# 默认架构
ARCH ?= riscv64

# 目标定义
RISCV64_TARGET = riscv64gc-unknown-none-elf
LOONGARCH64_TARGET = loongarch64-unknown-none

# 编译器和工具链
CARGO = cargo
OBJCOPY = rust-objcopy
OBJDUMP = rust-objdump

# 构建模式
MODE ?= debug
ifeq ($(MODE), release)
    BUILD_MODE = --release
    TARGET_DIR = target/$(TARGET)/release
else
    BUILD_MODE = 
    TARGET_DIR = target/$(TARGET)/debug
endif

# 根据架构设置目标
ifeq ($(ARCH), riscv64)
    TARGET = $(RISCV64_TARGET)
    KERNEL_ELF = $(TARGET_DIR)/kernel
    KERNEL_BIN = $(TARGET_DIR)/kernel.bin
else ifeq ($(ARCH), loongarch64)
    TARGET = $(LOONGARCH64_TARGET)
    KERNEL_ELF = $(TARGET_DIR)/kernel
    KERNEL_BIN = $(TARGET_DIR)/kernel.bin
else
    $(error Unsupported architecture: $(ARCH). Supported: riscv64, loongarch64)
endif

# 环境变量
export ARCH
export TARGET

# LoongArch64 向量指令配置
# 使用 'make ARCH=loongarch64 VECTOR=on' 来启用向量指令
ifeq ($(ARCH), loongarch64)
  ifeq ($(VECTOR), on)
    RUSTFLAGS += -C target-feature=+lsx,+lasx -C opt-level=2
    BUILD_INFO_MSG = "(Vector Extensions Enabled)"
  else
    RUSTFLAGS += -C target-feature=-lsx,-lasx -C opt-level=1
    BUILD_INFO_MSG = "(Vector Extensions Disabled)"
  endif
endif
export RUSTFLAGS

# 默认目标
.PHONY: all
all: build

# 构建内核
.PHONY: build
build:
	@echo 'Building kernel for $(ARCH) ($(TARGET)) $(BUILD_INFO_MSG)'
ifeq ($(ARCH),loongarch64)
	cd kernel && cargo +nightly build $(BUILD_MODE) --target $(TARGET) -Z build-std=core,alloc
else
	cd kernel && $(CARGO) build $(BUILD_MODE) --target $(TARGET)
endif
	@echo "Kernel built: $(KERNEL_ELF)"

# 生成二进制文件
.PHONY: bin
bin: build
	@echo "Generating binary file..."
	$(OBJCOPY) $(KERNEL_ELF) --strip-all -O binary $(KERNEL_BIN)
	@echo "Binary generated: $(KERNEL_BIN)"

# RISC-V64 特定目标
.PHONY: riscv64
riscv64:
	$(MAKE) ARCH=riscv64 build

.PHONY: riscv64-bin
riscv64-bin:
	$(MAKE) ARCH=riscv64 bin

# LoongArch64 特定目标
.PHONY: loongarch64
loongarch64:
	$(MAKE) ARCH=loongarch64 build

.PHONY: loongarch64-bin
loongarch64-bin:
	$(MAKE) ARCH=loongarch64 bin

# Debug 构建（显式）
.PHONY: debug
debug:
ifeq ($(ARCH), riscv64)
	$(MAKE) qemu-riscv64-debug
else ifeq ($(ARCH), loongarch64)
	$(MAKE) qemu-loongarch64-debug
else
	$(error Unsupported architecture: $(ARCH). Use 'make ARCH=<arch> debug')
endif

# 发布版本构建

.PHONY: riscv64-debug
riscv64-debug:
	$(MAKE) ARCH=riscv64 MODE=debug build

.PHONY: loongarch64-debug
loongarch64-debug:
	$(MAKE) ARCH=loongarch64 MODE=debug build

.PHONY: riscv64-release
riscv64-release:
	$(MAKE) ARCH=riscv64 MODE=release build

.PHONY: loongarch64-release
loongarch64-release:
	$(MAKE) ARCH=loongarch64 MODE=release build

# QEMU 运行 (需要安装相应的 QEMU)
.PHONY: run
run:
ifeq ($(ARCH), riscv64)
	$(MAKE) qemu-riscv64
else ifeq ($(ARCH), loongarch64)
	$(MAKE) qemu-loongarch64
else
	$(error Unsupported architecture: $(ARCH). Use 'make qemu-riscv64' or 'make qemu-loongarch64')
endif

# QEMU 运行带日志输出
.PHONY: runlog
runlog:
ifeq ($(ARCH), riscv64)
	$(MAKE) qemu-riscv64-log
else ifeq ($(ARCH), loongarch64)
	$(MAKE) qemu-loongarch64-log
else
	$(error Unsupported architecture: $(ARCH). Use 'make ARCH=<arch> runlog')
endif

.PHONY: qemu-riscv64
qemu-riscv64: riscv64-bin
	@echo "Running RISC-V64 kernel in QEMU..."
	qemu-system-riscv64 \
		-machine virt \
		-cpu rv64 \
		-smp 1 \
		-m 1G \
		-bios default \
		-kernel $(TARGET_DIR)/kernel.bin \
		-nographic

.PHONY: qemu-loongarch64
qemu-loongarch64: loongarch64
	@echo "Running LoongArch64 kernel in QEMU..."
	qemu-system-loongarch64 \
		-machine virt \
		-cpu la464 \
		-smp 1 \
		-m 128M \
		-kernel target/loongarch64-unknown-none/debug/kernel \
		-nographic

# QEMU 运行带日志输出
.PHONY: qemu-riscv64-log
qemu-riscv64-log: riscv64-bin
	@echo "Running RISC-V64 kernel in QEMU with logging..."
	qemu-system-riscv64 \
		-machine virt \
		-cpu rv64 \
		-smp 1 \
		-m 1G \
		-bios default \
		-kernel $(TARGET_DIR)/kernel.bin \
		-nographic \
		-d guest_errors,unimp,trace:* \
		-D qemu-riscv64.log

.PHONY: qemu-loongarch64-log
qemu-loongarch64-log: loongarch64
	@echo "Running LoongArch64 kernel in QEMU with logging..."
	qemu-system-loongarch64 \
		-machine virt \
		-cpu la464 \
		-smp 1 \
		-m 128M \
		-kernel target/loongarch64-unknown-none/debug/kernel \
		-nographic \
		-d guest_errors,int,exec \
		-D qemu-loongarch64.log

# 调试版本的 QEMU (带 GDB 服务器)
.PHONY: qemu-riscv64-debugqemu-loongarch64-debug
qemu-riscv64-debug: riscv64-bin
	@echo "Running RISC-V64 kernel in QEMU with GDB server..."
	qemu-system-riscv64 \
		-machine virt \
		-cpu rv64 \
		-smp 1 \
		-m 128M \
		-bios none \
		-kernel $(TARGET_DIR)/kernel.bin \
		-nographic \
		-s -S

.PHONY: qemu-loongarch64-debug
qemu-loongarch64-debug: loongarch64
	@echo "Running LoongArch64 kernel in QEMU with GDB server..."
	qemu-system-loongarch64 \
		-machine virt \
		-cpu la464 \
		-smp 1 \
		-m 128M \
		-kernel target/loongarch64-unknown-none/debug/kernel \
		-nographic \
		-s -S

# 反汇编
.PHONY: disasm
disasm: build
	@echo "Disassembling kernel..."
	$(OBJDUMP) -d $(KERNEL_ELF) > $(TARGET_DIR)/kernel.asm
	@echo "Disassembly saved to: $(TARGET_DIR)/kernel.asm"

# 清理
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	cd kernel && $(CARGO) clean
	rm -rf target/

# 检查工具链
.PHONY: check-toolchain
check-toolchain:
	@echo "Checking toolchain..."
	@which $(CARGO) > /dev/null || (echo "Error: cargo not found" && exit 1)
	@which $(OBJCOPY) > /dev/null || (echo "Error: rust-objcopy not found" && exit 1)
	@which $(OBJDUMP) > /dev/null || (echo "Error: rust-objdump not found" && exit 1)
	@echo "Toolchain check passed!"

# 安装目标架构支持
.PHONY: install-targets
install-targets:
	@echo "Installing Rust targets..."
	rustup target add $(RISCV64_TARGET)
	rustup target add $(LOONGARCH64_TARGET)
	@echo "Targets installed!"

# 帮助信息
.PHONY: help
help:
	@echo "Echos Kernel Build System"
	@echo ""
	@echo "Usage:"
	@echo "  make [ARCH=<arch>] [MODE=<mode>] <target>"
	@echo ""
	@echo "Architectures:"
	@echo "  riscv64      - RISC-V 64-bit (default)"
	@echo "  loongarch64  - LoongArch 64-bit"
	@echo ""
	@echo "Modes:"
	@echo "  debug        - Debug build (default)"
	@echo "  release      - Release build"
	@echo ""
	@echo "Targets:"
	@echo "  build        - Build kernel for current architecture"
	@echo "  bin          - Build kernel and generate binary"
	@echo "  run          - Run kernel in QEMU for current architecture"
	@echo "  runlog       - Run kernel in QEMU with detailed logging"
	@echo "  debug        - Run kernel in QEMU with GDB server"
	@echo "  riscv64      - Build for RISC-V64"
	@echo "  loongarch64  - Build for LoongArch64"
	@echo "  release      - Build release version"
	@echo "  qemu-riscv64 - Run RISC-V64 kernel in QEMU"
	@echo "  qemu-loongarch64 - Run LoongArch64 kernel in QEMU"
	@echo "  disasm       - Generate disassembly"
	@echo "  clean        - Clean build artifacts"
	@echo "  install-targets - Install Rust targets"
	@echo "  check-toolchain - Check if required tools are available"
	@echo ""
	@echo "Examples:"
	@echo "  make riscv64"
	@echo "  make ARCH=loongarch64 MODE=release build"
	@echo "  make qemu-riscv64"
