//! LoongArch64 boot module
//!
//! This module contains the boot code and linker script for LoongArch64.

use core::arch::global_asm;

// Include the boot assembly code
global_asm!(include_str!("boot.asm"));

// Export symbols for linker script
unsafe extern "C" {
    fn sbss();
    fn ebss();
    fn bootstack();
}

/// LoongArch64-specific boot initialization
pub fn init() {
    // Any LoongArch64-specific initialization can go here
}
