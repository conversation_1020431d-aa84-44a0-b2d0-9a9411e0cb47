# LoongArch64 Boot Assembly
# Entry point for LoongArch64 kernel
# Translated from Rust inline assembly

.section .text.entry
.globl _start
_start:
    # Initialize DMW (Direct Mapping Windows) - equivalent to init_dwm!() macro
    # DMW0: 0x8000000000000000-0x8FFFFFFFFFFFFFFF -> 0x0000000000000000-0x0FFFFFFFFFFFFFFF
    li.d    $t0, 0x8000000000000000
    ori     $t0, $t0, 0x9           # Mat=0, PLV0=1, PLV3=1
    csrwr   $t0, 0x180              # LOONGARCH_CSR_DMW0
    
    # DMW1: 0x9000000000000000-0x9FFFFFFFFFFFFFFF -> 0x0000000000000000-0x0FFFFFFFFFFFFFFF  
    li.d    $t0, 0x9000000000000000
    ori     $t0, $t0, 0x19          # Mat=1, PLV0=1, PLV3=1
    csrwr   $t0, 0x181              # LOONGARCH_CSR_DMW1

    # Enable PG and configure processor state
    li.w    $t0, 0xb0               # PLV=0, IE=0, PG=1
    csrwr   $t0, 0x0                # LOONGARCH_CSR_CRMD
    li.w    $t0, 0x00               # PLV=0, PIE=0, PWE=0
    csrwr   $t0, 0x1                # LOONGARCH_CSR_PRMD
    li.w    $t0, 0x00               # FPE=0, SXE=0, ASXE=0, BTE=0
    csrwr   $t0, 0x2                # LOONGARCH_CSR_EUEN

    # Set up stack pointer
    la.global $sp, bstack_top
    
    # Clear BSS (sbss .. ebss)
    la.global $t0, sbss
    la.global $t1, ebss
1:
    bge     $t0, $t1, 2f
    st.d    $zero, $t0, 0
    addi.d  $t0, $t0, 8
    b       1b
2:

    # Set GP (r28) to GOT base expected by Rust
    la.global $r28, __global_pointer$

    # Get CPU ID and jump to rust_main
    csrrd   $a0, 0x20               # cpuid
    la.global $t0, rust_main
    jirl    $zero, $t0, 0

# Boot stack section
.section .bss.stack
.align 12   # page align
.global bstack
bstack:
    .space 4096 * 4 * 8            # 128KB stack space
.global bstack_top
bstack_top: