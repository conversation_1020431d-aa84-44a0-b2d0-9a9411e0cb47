//! Bitmap-based page frame allocator with segmented memory support
//!
//! This module provides a bitmap-based physical page frame allocator
//! that can allocate and deallocate physical memory pages across multiple
//! non-contiguous memory regions.

use super::address::{PhysAddr, PhysPageNum};
use core::sync::atomic::{AtomicUsize, Ordering};
extern crate alloc;
use alloc::vec::Vec;
use alloc::vec;

/// Memory region descriptor for segmented memory management
#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy)]
pub struct MemoryRegion {
    /// Start physical page number
    pub start_ppn: PhysPageNum,
    /// End physical page number (exclusive)
    pub end_ppn: PhysPageNum,
    /// Start address of the bitmap for this region
    pub bitmap_start: usize,
    /// Number of pages in this region
    pub page_count: usize,
}

impl MemoryRegion {
    /// Create a new memory region
    pub fn new(start_addr: PhysAddr, size: usize) -> Self {
        let start_ppn = start_addr.to_ppn();
        let page_count = (size + 0xfff) / 0x1000; // Round up to page boundary
        let end_ppn = PhysPageNum::new(start_ppn.as_usize() + page_count);

        Self {
            start_ppn,
            end_ppn,
            bitmap_start: 0, // Will be set during initialization
            page_count,
        }
    }

    /// Check if a page number belongs to this region
    pub fn contains(&self, ppn: PhysPageNum) -> bool {
        ppn >= self.start_ppn && ppn < self.end_ppn
    }

    /// Get the bit index for a page within this region
    pub fn get_bit_index(&self, ppn: PhysPageNum) -> Option<usize> {
        if self.contains(ppn) {
            Some(ppn.as_usize() - self.start_ppn.as_usize())
        } else {
            None
        }
    }
}

/// Segmented bitmap-based page frame allocator
pub struct SegmentedFrameAllocator {
    /// Memory regions managed by this allocator
    regions: Vec<MemoryRegion>,
    /// Bitmap to track allocated frames (1 = allocated, 0 = free)
    bitmap: &'static mut [u8],
    /// Total number of allocated frames across all regions
    allocated_count: AtomicUsize,
    /// Total number of pages across all regions
    total_pages: usize,
}

impl SegmentedFrameAllocator {
    /// Create a new segmented frame allocator
    ///
    /// # Arguments
    /// * `memory_regions` - List of (start_addr, size) tuples for each memory region
    /// * `bitmap_addr` - Physical address where bitmap will be stored
    ///
    /// # Safety
    /// The caller must ensure that:
    /// - All memory regions are valid physical memory
    /// - The bitmap_addr points to valid writable memory
    /// - The bitmap has enough space for all pages across all regions
    pub unsafe fn new(
        memory_regions: Vec<(PhysAddr, usize)>,
        bitmap_addr: PhysAddr,
    ) -> Self {
        let mut regions = Vec::new();
        let mut total_pages = 0;
        let mut bitmap_offset = 0;

        // Create memory region descriptors
        for (start_addr, size) in memory_regions {
            let mut region = MemoryRegion::new(start_addr, size);
            region.bitmap_start = bitmap_offset;
            bitmap_offset += (region.page_count + 7) / 8; // Round up to nearest byte
            total_pages += region.page_count;
            regions.push(region);
        }

        let bitmap_bytes = (total_pages + 7) / 8; // Round up to nearest byte

        // Initialize bitmap memory
        let bitmap = unsafe {
            core::slice::from_raw_parts_mut(
                bitmap_addr.as_mut_ptr::<u8>(),
                bitmap_bytes,
            )
        };

        // Clear all bits (mark all pages as free)
        bitmap.fill(0);

        Self {
            regions,
            bitmap,
            allocated_count: AtomicUsize::new(0),
            total_pages,
        }
    }

    /// Find the region that contains the given page number
    fn find_region(&self, ppn: PhysPageNum) -> Option<&MemoryRegion> {
        self.regions.iter().find(|region| region.contains(ppn))
    }

    /// Check if a page is allocated
    fn is_allocated(&self, ppn: PhysPageNum) -> bool {
        if let Some(region) = self.find_region(ppn) {
            if let Some(bit_index) = region.get_bit_index(ppn) {
                let global_bit_index = region.bitmap_start * 8 + bit_index;
                let byte_index = global_bit_index / 8;
                let bit_offset = global_bit_index % 8;
                (self.bitmap[byte_index] & (1 << bit_offset)) != 0
            } else {
                false
            }
        } else {
            false
        }
    }

    /// Set allocation status for a page
    fn set_allocated(&mut self, ppn: PhysPageNum, allocated: bool) {
        if let Some(region) = self.find_region(ppn) {
            if let Some(bit_index) = region.get_bit_index(ppn) {
                let global_bit_index = region.bitmap_start * 8 + bit_index;
                let byte_index = global_bit_index / 8;
                let bit_offset = global_bit_index % 8;

                if allocated {
                    self.bitmap[byte_index] |= 1 << bit_offset;
                } else {
                    self.bitmap[byte_index] &= !(1 << bit_offset);
                }
            }
        }
    }

    /// Allocate a single page frame
    ///
    /// Returns the physical page number of the allocated frame, or None if no frames are available
    pub fn alloc(&mut self) -> Option<PhysPageNum> {
        // Try to find a free page in any region
        for region in &self.regions {
            for ppn_offset in 0..region.page_count {
                let ppn = PhysPageNum::new(region.start_ppn.as_usize() + ppn_offset);
                if !self.is_allocated(ppn) {
                    self.set_allocated(ppn, true);
                    self.allocated_count.fetch_add(1, Ordering::Relaxed);
                    return Some(ppn);
                }
            }
        }
        None
    }

    /// Allocate multiple contiguous page frames
    ///
    /// Returns the starting physical page number of the allocated frames, or None if not enough
    /// contiguous frames are available within a single region
    pub fn alloc_contiguous(&mut self, count: usize) -> Option<PhysPageNum> {
        if count == 0 {
            return None;
        }

        // Try to find contiguous free pages within each region
        for region in &self.regions {
            if count > region.page_count {
                continue; // This region is too small
            }

            // Find contiguous free pages within this region
            'outer: for start_offset in 0..=(region.page_count - count) {
                let start_ppn = PhysPageNum::new(region.start_ppn.as_usize() + start_offset);

                // Check if all pages in the range are free
                for i in 0..count {
                    let ppn = start_ppn.add(i);
                    if self.is_allocated(ppn) {
                        continue 'outer;
                    }
                }

                // All pages are free, allocate them
                for i in 0..count {
                    let ppn = start_ppn.add(i);
                    self.set_allocated(ppn, true);
                }
                self.allocated_count.fetch_add(count, Ordering::Relaxed);
                return Some(start_ppn);
            }
        }

        None
    }

    /// Deallocate a single page frame
    ///
    /// # Arguments
    /// * `ppn` - Physical page number to deallocate
    ///
    /// Returns true if the page was successfully deallocated, false if it was not allocated
    pub fn dealloc(&mut self, ppn: PhysPageNum) -> bool {
        if self.is_allocated(ppn) {
            self.set_allocated(ppn, false);
            self.allocated_count.fetch_sub(1, Ordering::Relaxed);
            true
        } else {
            false
        }
    }

    /// Deallocate multiple contiguous page frames
    ///
    /// # Arguments
    /// * `start_ppn` - Starting physical page number
    /// * `count` - Number of pages to deallocate
    ///
    /// Returns the number of pages actually deallocated
    pub fn dealloc_contiguous(&mut self, start_ppn: PhysPageNum, count: usize) -> usize {
        let mut deallocated = 0;

        for i in 0..count {
            let ppn = start_ppn.add(i);
            if self.dealloc(ppn) {
                deallocated += 1;
            }
        }

        deallocated
    }

    /// Get the number of allocated pages
    pub fn allocated_count(&self) -> usize {
        self.allocated_count.load(Ordering::Relaxed)
    }

    /// Get the total number of manageable pages across all regions
    pub fn total_pages(&self) -> usize {
        self.total_pages
    }

    /// Get the number of free pages
    pub fn free_count(&self) -> usize {
        self.total_pages - self.allocated_count()
    }

    /// Check if the allocator is full
    pub fn is_full(&self) -> bool {
        self.allocated_count() == self.total_pages()
    }

    /// Check if the allocator is empty
    pub fn is_empty(&self) -> bool {
        self.allocated_count() == 0
    }

    /// Get allocator information
    pub fn info(&self) -> SegmentedFrameAllocatorInfo {
        SegmentedFrameAllocatorInfo {
            regions: self.regions.clone(),
            total_pages: self.total_pages(),
            allocated_pages: self.allocated_count(),
            free_pages: self.free_count(),
        }
    }

    /// Mark a range of pages as allocated (for reserved memory regions)
    pub fn mark_allocated_range(&mut self, start_ppn: PhysPageNum, count: usize) -> usize {
        let mut marked = 0;
        for i in 0..count {
            let ppn = start_ppn.add(i);
            if !self.is_allocated(ppn) {
                self.set_allocated(ppn, true);
                self.allocated_count.fetch_add(1, Ordering::Relaxed);
                marked += 1;
            }
        }
        marked
    }

    /// Get memory regions managed by this allocator
    pub fn get_regions(&self) -> &Vec<MemoryRegion> {
        &self.regions
    }

    /// Print detailed information about all memory regions
    pub fn print_regions_info(&self) {
        crate::framelog!("=== Segmented Frame Allocator Regions ===");
        for (i, region) in self.regions.iter().enumerate() {
            let region_allocated = self.count_allocated_in_region(region);
            crate::framelog!("Region {}: 0x{:x} - 0x{:x} ({} pages, {} allocated, {} free)",
                i,
                region.start_ppn.as_usize() * 0x1000,
                region.end_ppn.as_usize() * 0x1000,
                region.page_count,
                region_allocated,
                region.page_count - region_allocated
            );
        }
        crate::framelog!("Total: {} pages, {} allocated, {} free",
            self.total_pages(),
            self.allocated_count(),
            self.free_count()
        );
    }

    /// Count allocated pages in a specific region
    fn count_allocated_in_region(&self, region: &MemoryRegion) -> usize {
        let mut count = 0;
        for ppn_offset in 0..region.page_count {
            let ppn = PhysPageNum::new(region.start_ppn.as_usize() + ppn_offset);
            if self.is_allocated(ppn) {
                count += 1;
            }
        }
        count
    }
}

/// Segmented frame allocator information
#[derive(Debug, Clone)]
pub struct SegmentedFrameAllocatorInfo {
    pub regions: Vec<MemoryRegion>,
    pub total_pages: usize,
    pub allocated_pages: usize,
    pub free_pages: usize,
}

impl core::fmt::Display for SegmentedFrameAllocatorInfo {
    fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
        write!(
            f,
            "SegmentedFrameAllocator: {} regions, total={} allocated={} free={}",
            self.regions.len(),
            self.total_pages,
            self.allocated_pages,
            self.free_pages
        )
    }
}

/// Bitmap-based page frame allocator (legacy single-region allocator)
pub struct BitmapFrameAllocator {
    /// Start physical page number
    start_ppn: PhysPageNum,
    /// End physical page number (exclusive)
    end_ppn: PhysPageNum,
    /// Bitmap to track allocated frames (1 = allocated, 0 = free)
    bitmap: &'static mut [u8],
    /// Number of allocated frames
    allocated_count: AtomicUsize,
}

impl BitmapFrameAllocator {
    /// Create a new bitmap frame allocator
    ///
    /// # Arguments
    /// * `start_addr` - Start physical address of manageable memory
    /// * `end_addr` - End physical address of manageable memory (exclusive)
    /// * `bitmap_addr` - Physical address where bitmap will be stored
    ///
    /// # Safety
    /// The caller must ensure that:
    /// - The memory range [start_addr, end_addr) is valid physical memory
    /// - The bitmap_addr points to valid writable memory
    /// - The bitmap has enough space for (end_addr - start_addr) / PAGE_SIZE bits
    pub unsafe fn new(
        start_addr: PhysAddr,
        end_addr: PhysAddr,
        bitmap_addr: PhysAddr,
    ) -> Self {
        let start_ppn = start_addr.to_ppn();
        let end_ppn = end_addr.to_ppn();
        let total_pages = end_ppn.as_usize() - start_ppn.as_usize();
        let bitmap_bytes = (total_pages + 7) / 8; // Round up to nearest byte

        // Initialize bitmap memory
        let bitmap = unsafe {
            core::slice::from_raw_parts_mut(
                bitmap_addr.as_mut_ptr::<u8>(),
                bitmap_bytes,
            )
        };

        // Clear all bits (mark all pages as free)
        bitmap.fill(0);

        Self {
            start_ppn,
            end_ppn,
            bitmap,
            allocated_count: AtomicUsize::new(0),
        }
    }

    /// Get the bit index for a given page number
    fn get_bit_index(&self, ppn: PhysPageNum) -> Option<usize> {
        if ppn >= self.start_ppn && ppn < self.end_ppn {
            Some(ppn.as_usize() - self.start_ppn.as_usize())
        } else {
            None
        }
    }

    /// Check if a page is allocated
    fn is_allocated(&self, ppn: PhysPageNum) -> bool {
        if let Some(bit_index) = self.get_bit_index(ppn) {
            let byte_index = bit_index / 8;
            let bit_offset = bit_index % 8;
            (self.bitmap[byte_index] & (1 << bit_offset)) != 0
        } else {
            false
        }
    }

    /// Set allocation status for a page
    fn set_allocated(&mut self, ppn: PhysPageNum, allocated: bool) {
        if let Some(bit_index) = self.get_bit_index(ppn) {
            let byte_index = bit_index / 8;
            let bit_offset = bit_index % 8;

            if allocated {
                self.bitmap[byte_index] |= 1 << bit_offset;
            } else {
                self.bitmap[byte_index] &= !(1 << bit_offset);
            }
        }
    }

    /// Allocate a single page frame
    ///
    /// Returns the physical page number of the allocated frame, or None if no frames are available
    pub fn alloc(&mut self) -> Option<PhysPageNum> {
        // Find first free page
        for ppn_offset in 0..(self.end_ppn.as_usize() - self.start_ppn.as_usize()) {
            let ppn = PhysPageNum::new(self.start_ppn.as_usize() + ppn_offset);
            if !self.is_allocated(ppn) {
                self.set_allocated(ppn, true);
                self.allocated_count.fetch_add(1, Ordering::Relaxed);
                return Some(ppn);
            }
        }
        None
    }

    /// Allocate multiple contiguous page frames
    ///
    /// Returns the starting physical page number of the allocated frames, or None if not enough
    /// contiguous frames are available
    pub fn alloc_contiguous(&mut self, count: usize) -> Option<PhysPageNum> {
        if count == 0 {
            return None;
        }

        let total_pages = self.end_ppn.as_usize() - self.start_ppn.as_usize();
        if count > total_pages {
            return None;
        }

        // Find contiguous free pages
        'outer: for start_offset in 0..=(total_pages - count) {
            let start_ppn = PhysPageNum::new(self.start_ppn.as_usize() + start_offset);

            // Check if all pages in the range are free
            for i in 0..count {
                let ppn = start_ppn.add(i);
                if self.is_allocated(ppn) {
                    continue 'outer;
                }
            }

            // All pages are free, allocate them
            for i in 0..count {
                let ppn = start_ppn.add(i);
                self.set_allocated(ppn, true);
            }
            self.allocated_count.fetch_add(count, Ordering::Relaxed);
            return Some(start_ppn);
        }

        None
    }

    /// Deallocate a single page frame
    ///
    /// # Arguments
    /// * `ppn` - Physical page number to deallocate
    ///
    /// Returns true if the page was successfully deallocated, false if it was not allocated
    pub fn dealloc(&mut self, ppn: PhysPageNum) -> bool {
        if self.is_allocated(ppn) {
            self.set_allocated(ppn, false);
            self.allocated_count.fetch_sub(1, Ordering::Relaxed);
            true
        } else {
            false
        }
    }

    /// Deallocate multiple contiguous page frames
    ///
    /// # Arguments
    /// * `start_ppn` - Starting physical page number
    /// * `count` - Number of pages to deallocate
    ///
    /// Returns the number of pages actually deallocated
    pub fn dealloc_contiguous(&mut self, start_ppn: PhysPageNum, count: usize) -> usize {
        let mut deallocated = 0;

        for i in 0..count {
            let ppn = start_ppn.add(i);
            if self.dealloc(ppn) {
                deallocated += 1;
            }
        }

        deallocated
    }

    /// Get the number of allocated pages
    pub fn allocated_count(&self) -> usize {
        self.allocated_count.load(Ordering::Relaxed)
    }

    /// Get the total number of manageable pages
    pub fn total_pages(&self) -> usize {
        self.end_ppn.as_usize() - self.start_ppn.as_usize()
    }

    /// Get the number of free pages
    pub fn free_count(&self) -> usize {
        self.total_pages() - self.allocated_count()
    }

    /// Check if the allocator is full
    pub fn is_full(&self) -> bool {
        self.allocated_count() == self.total_pages()
    }

    /// Check if the allocator is empty
    pub fn is_empty(&self) -> bool {
        self.allocated_count() == 0
    }

    /// Get allocator information
    pub fn info(&self) -> FrameAllocatorInfo {
        FrameAllocatorInfo {
            start_ppn: self.start_ppn,
            end_ppn: self.end_ppn,
            total_pages: self.total_pages(),
            allocated_pages: self.allocated_count(),
            free_pages: self.free_count(),
        }
    }

    /// Find the first free page starting from a given page number
    pub fn find_free_from(&self, start_ppn: PhysPageNum) -> Option<PhysPageNum> {
        if start_ppn < self.start_ppn {
            return self.find_free_from(self.start_ppn);
        }

        for ppn_offset in (start_ppn.as_usize() - self.start_ppn.as_usize())..(self.end_ppn.as_usize() - self.start_ppn.as_usize()) {
            let ppn = PhysPageNum::new(self.start_ppn.as_usize() + ppn_offset);
            if !self.is_allocated(ppn) {
                return Some(ppn);
            }
        }
        None
    }

    /// Mark a range of pages as allocated (for reserved memory regions)
    pub fn mark_allocated_range(&mut self, start_ppn: PhysPageNum, count: usize) -> usize {
        let mut marked = 0;
        for i in 0..count {
            let ppn = start_ppn.add(i);
            if !self.is_allocated(ppn) {
                self.set_allocated(ppn, true);
                self.allocated_count.fetch_add(1, Ordering::Relaxed);
                marked += 1;
            }
        }
        marked
    }

    /// Create a test allocator with a given bitmap (for testing purposes)
    pub unsafe fn new_for_test(
        start_ppn: PhysPageNum,
        end_ppn: PhysPageNum,
        bitmap: &'static mut [u8],
    ) -> Self {
        // Clear all bits (mark all pages as free)
        bitmap.fill(0);
        
        Self {
            start_ppn,
            end_ppn,
            bitmap,
            allocated_count: AtomicUsize::new(0),
        }
    }
}

/// Frame allocator information
#[derive(Debug, Clone, Copy)]
pub struct FrameAllocatorInfo {
    pub start_ppn: PhysPageNum,
    pub end_ppn: PhysPageNum,
    pub total_pages: usize,
    pub allocated_pages: usize,
    pub free_pages: usize,
}

impl core::fmt::Display for FrameAllocatorInfo {
    fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
        write!(
            f,
            "FrameAllocator: [{} - {}) total={} allocated={} free={}",
            self.start_ppn,
            self.end_ppn,
            self.total_pages,
            self.allocated_pages,
            self.free_pages
        )
    }
}

/// Global frame allocator instance (using segmented allocator)
static mut FRAME_ALLOCATOR: Option<SegmentedFrameAllocator> = None;

/// Initialize the global frame allocator with multiple memory regions
///
/// # Safety
/// This function should only be called once during kernel initialization
pub unsafe fn init_segmented_frame_allocator(
    memory_regions: Vec<(PhysAddr, usize)>,
    bitmap_addr: PhysAddr,
) {
    unsafe {
        FRAME_ALLOCATOR = Some(SegmentedFrameAllocator::new(memory_regions, bitmap_addr));
    }
}

/// Initialize the global frame allocator (legacy single-region version)
///
/// # Safety
/// This function should only be called once during kernel initialization
pub unsafe fn init_frame_allocator(
    start_addr: PhysAddr,
    end_addr: PhysAddr,
    bitmap_addr: PhysAddr,
) {
    let size = end_addr.as_usize() - start_addr.as_usize();
    let memory_regions = vec![(start_addr, size)];
    unsafe {
        init_segmented_frame_allocator(memory_regions, bitmap_addr);
    }
}

/// Allocate a single page frame using the global allocator
pub fn alloc_frame() -> Option<PhysPageNum> {
    unsafe {
        let allocator_ptr = core::ptr::addr_of_mut!(FRAME_ALLOCATOR);
        let allocator = (*allocator_ptr).as_mut()?;
        allocator.alloc()
    }
}

/// Allocate multiple contiguous page frames using the global allocator
pub fn alloc_frames(count: usize) -> Option<PhysPageNum> {
    unsafe {
        let allocator_ptr = core::ptr::addr_of_mut!(FRAME_ALLOCATOR);
        let allocator = (*allocator_ptr).as_mut()?;
        allocator.alloc_contiguous(count)
    }
}

/// Deallocate a single page frame using the global allocator
pub fn dealloc_frame(ppn: PhysPageNum) -> bool {
    unsafe {
        let allocator_ptr = core::ptr::addr_of_mut!(FRAME_ALLOCATOR);
        if let Some(allocator) = (*allocator_ptr).as_mut() {
            allocator.dealloc(ppn)
        } else {
            false
        }
    }
}

/// Deallocate multiple contiguous page frames using the global allocator
pub fn dealloc_frames(start_ppn: PhysPageNum, count: usize) -> usize {
    unsafe {
        let allocator_ptr = core::ptr::addr_of_mut!(FRAME_ALLOCATOR);
        if let Some(allocator) = (*allocator_ptr).as_mut() {
            allocator.dealloc_contiguous(start_ppn, count)
        } else {
            0
        }
    }
}

/// Get frame allocator statistics
pub fn frame_allocator_info() -> Option<SegmentedFrameAllocatorInfo> {
    unsafe {
        let allocator_ptr = core::ptr::addr_of!(FRAME_ALLOCATOR);
        if let Some(allocator) = (*allocator_ptr).as_ref() {
            Some(allocator.info())
        } else {
            None
        }
    }
}

/// Mark a range of pages as allocated (for reserved memory regions)
pub fn mark_allocated_range(start_ppn: PhysPageNum, count: usize) -> usize {
    unsafe {
        let allocator_ptr = core::ptr::addr_of_mut!(FRAME_ALLOCATOR);
        if let Some(allocator) = (*allocator_ptr).as_mut() {
            allocator.mark_allocated_range(start_ppn, count)
        } else {
            0
        }
    }
}

/// Print detailed information about all memory regions
pub fn print_frame_allocator_info() {
    unsafe {
        let allocator_ptr = core::ptr::addr_of!(FRAME_ALLOCATOR);
        if let Some(allocator) = (*allocator_ptr).as_ref() {
            allocator.print_regions_info();
        } else {
            crate::framelog!("Frame allocator not initialized");
        }
    }
}

/// Get memory regions managed by the frame allocator
pub fn get_frame_allocator_regions() -> Option<Vec<MemoryRegion>> {
    unsafe {
        let allocator_ptr = core::ptr::addr_of!(FRAME_ALLOCATOR);
        if let Some(allocator) = (*allocator_ptr).as_ref() {
            Some(allocator.get_regions().clone())
        } else {
            None
        }
    }
}

/// Initialize frame allocator from DTB memory regions
///
/// This function reads memory regions from the DTB and initializes the segmented frame allocator,
/// excluding the kernel-occupied memory regions
///
/// # Safety
/// This function should only be called once during kernel initialization
pub unsafe fn init_frame_allocator_from_dtb(bitmap_addr: PhysAddr) -> Result<(), &'static str> {
    // Get memory regions from DTB
    let dtb_regions = crate::dtb::get_memory_regions();

    if dtb_regions.is_empty() {
        return Err("No memory regions found in DTB");
    }

    // Get kernel memory boundaries (these are virtual addresses)
    unsafe extern "C" {
        fn start();
        fn end();
    }
    let kernel_start_virt = start as usize;
    let kernel_end_virt = end as usize;

    // Convert virtual addresses to physical addresses
    let (kernel_start_phys, kernel_end_phys) = {
        #[cfg(target_arch = "riscv64")]
        {
            // RISC-V64: kernel virtual address space starts at 0xffffffffc0000000
            // Physical memory starts at 0x80000000
            let virt_base = 0xffffffffc0000000;
            let phys_base = 0x80000000;

            let start_phys = if kernel_start_virt >= virt_base {
                kernel_start_virt - virt_base + phys_base
            } else {
                kernel_start_virt // Already physical
            };

            let end_phys = if kernel_end_virt >= virt_base {
                kernel_end_virt - virt_base + phys_base
            } else {
                kernel_end_virt // Already physical
            };

            (start_phys, end_phys)
        }

        #[cfg(target_arch = "loongarch64")]
        {
            // LoongArch64: kernel virtual address space starts at 0x9000000000000000
            // Physical memory starts at 0x80000000
            // Virtual mapping: 0x9000000000200000 -> 0x80200000
            let virt_base = 0x9000000000000000;
            let phys_base = 0x80000000;

            let start_phys = if kernel_start_virt >= virt_base {
                kernel_start_virt - virt_base + phys_base
            } else {
                kernel_start_virt // Already physical
            };

            let end_phys = if kernel_end_virt >= virt_base {
                kernel_end_virt - virt_base + phys_base
            } else {
                kernel_end_virt // Already physical
            };

            (start_phys, end_phys)
        }
    };

    // Define architecture-specific reserved regions (BIOS/firmware)
    let reserved_regions = {
        #[cfg(target_arch = "riscv64")]
        {
            // RISC-V: BIOS/firmware region from 0x80000000 to 0x80200000 (2MB)
            vec![(0x80000000, 0x80200000)]
        }

        #[cfg(target_arch = "loongarch64")]
        {
            // LoongArch64: DTB region at 0x100000, reserve some space around it
            // Also reserve BIOS/firmware region similar to RISC-V
            vec![
                (0x100000, 0x200000),      // DTB reserved region
                (0x80000000, 0x80200000),  // BIOS/firmware region
            ]
        }
    };

    // Convert DTB regions to frame allocator format, excluding kernel memory and reserved regions
    let mut memory_regions = Vec::new();
    for (base, size) in dtb_regions {
        let region_start = base as usize;
        let region_end = region_start + size as usize;

        // Split the region to exclude kernel memory and reserved regions
        let mut current_regions = vec![(region_start, region_end)];

        // First, exclude kernel memory
        let mut temp_regions = Vec::new();
        for (start, end) in current_regions {
            if end <= kernel_start_phys || start >= kernel_end_phys {
                // No overlap with kernel
                temp_regions.push((start, end));
            } else {
                // There's overlap with kernel, split the region
                if start < kernel_start_phys {
                    // Add memory before kernel
                    temp_regions.push((start, kernel_start_phys));
                }
                if end > kernel_end_phys {
                    // Add memory after kernel
                    temp_regions.push((kernel_end_phys, end));
                }
                crate::framelog!("Excluded kernel: 0x{:x} - 0x{:x}", kernel_start_phys, kernel_end_phys);
            }
        }

        // Then, exclude reserved regions (BIOS/firmware)
        current_regions = temp_regions;
        temp_regions = Vec::new();

        for (start, end) in current_regions {
            let mut current_start = start;

            for (reserved_start, reserved_end) in &reserved_regions {
                if current_start >= end {
                    break; // No more region to process
                }

                if end <= *reserved_start || current_start >= *reserved_end {
                    // No overlap with this reserved region
                    continue;
                }

                // There's overlap with reserved region
                if current_start < *reserved_start {
                    // Add memory before reserved region
                    temp_regions.push((current_start, *reserved_start));
                }

                // Skip the reserved region
                current_start = *reserved_end;
                crate::framelog!("Excluded reserved: 0x{:x} - 0x{:x}", reserved_start, reserved_end);
            }

            // Add remaining memory after all reserved regions
            if current_start < end {
                temp_regions.push((current_start, end));
            }
        }

        // Add all valid regions to memory_regions
        for (start, end) in temp_regions {
            if start < end {
                let start_addr = PhysAddr::new(start);
                let region_size = end - start;
                memory_regions.push((start_addr, region_size));
            }
        }
    }

    if memory_regions.is_empty() {
        return Err("No usable memory regions after excluding kernel");
    }

    // Initialize the segmented frame allocator
    unsafe {
        init_segmented_frame_allocator(memory_regions, bitmap_addr);
    }

    Ok(())
}