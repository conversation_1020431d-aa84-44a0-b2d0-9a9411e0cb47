//! Frame allocator comprehensive test module
//!
//! Tests for bitmap-based page frame allocator

extern crate alloc;
use crate::mem::address::{PhysPageNum};
use crate::mem::frame::{alloc_frame, dealloc_frame, alloc_frames, dealloc_frames};

/// Test bitmap frame allocator functionality with comprehensive coverage
pub fn test_bitmap_frame_allocator() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    

    
    // Test 1: Basic single frame allocation
    test_count += 1;
    let frame1 = alloc_frame();
    if frame1.is_none() {
        crate::println!("[FAIL] Test {}: First frame allocation failed", test_count);
        all_passed = false;
    }
    
    // Test 2: Second frame allocation
    test_count += 1;
    let frame2 = alloc_frame();
    if frame2.is_none() {
        crate::println!("[FAIL] Test {}: Second frame allocation failed", test_count);
        all_passed = false;
    }
    
    // Test 3: Frames should be different
    test_count += 1;
    if frame1 == frame2 {
        crate::println!("[FAIL] Test {}: Allocated frames are identical", test_count);
        all_passed = false;
    }
    
    // Test 4: Frame addresses should be reasonable (basic sanity check)
    test_count += 1;
    if let Some(ppn1) = frame1 {
        if ppn1.0 == 0 {
            crate::println!("[FAIL] Test {}: Frame1 address is zero: {:?}", test_count, ppn1);
            all_passed = false;
        }
    }
    if let Some(ppn2) = frame2 {
        if ppn2.0 == 0 {
            crate::println!("[FAIL] Test {}: Frame2 address is zero: {:?}", test_count, ppn2);
            all_passed = false;
        }
    }
    
    // Test 5: Deallocation and reallocation
    test_count += 1;
    if let Some(ppn1) = frame1 {
        dealloc_frame(ppn1);
    }
    if let Some(ppn2) = frame2 {
        dealloc_frame(ppn2);
    }
    
    let frame3 = alloc_frame();
    if frame3.is_none() {
        crate::println!("[FAIL] Test {}: Reallocation after deallocation failed", test_count);
        all_passed = false;
    }
    
    // Test 6: Bulk allocation test
    test_count += 1;
    let mut allocated_frames = alloc::vec::Vec::new();
    let target_allocs = 500;  // Try to allocate 500 frames
    
    for i in 0..target_allocs {
        if let Some(frame) = alloc_frame() {
            allocated_frames.push(frame);
        } else {
            crate::println!("[INFO] Test {}: Allocation exhausted after {} frames", test_count, i);
            break;
        }
    }
    
    if allocated_frames.len() < 100 {
        crate::println!("[FAIL] Test {}: Too few frames allocated: {}", test_count, allocated_frames.len());
        all_passed = false;
    }
    
    // Test 7: Uniqueness check for allocated frames
    test_count += 1;
    let mut unique_check_passed = true;
    for i in 0..allocated_frames.len() {
        for j in (i + 1)..allocated_frames.len() {
            if allocated_frames[i] == allocated_frames[j] {
                crate::println!("[FAIL] Test {}: Duplicate frames detected at indices {} and {}", test_count, i, j);
                unique_check_passed = false;
                all_passed = false;
                break;
            }
        }
        if !unique_check_passed {
            break;
        }
    }
    
    // Test 8: Contiguous allocation test using alloc_frames
    test_count += 1;
    let contiguous_count = 8;
    let contiguous_frames = alloc_frames(contiguous_count);
    if contiguous_frames.is_none() {
        crate::println!("[FAIL] Test {}: Contiguous allocation of {} frames failed", test_count, contiguous_count);
        all_passed = false;
    } else if let Some(start_ppn) = contiguous_frames {
        // Verify contiguity arithmetic
        for i in 1..contiguous_count {
            let expected_ppn = PhysPageNum::new(start_ppn.0 + i);
            if expected_ppn != PhysPageNum::new(start_ppn.0 + i) {
                crate::println!("[FAIL] Test {}: Contiguous frame arithmetic failed", test_count);
                all_passed = false;
                break;
            }
        }
        
        // Deallocate contiguous frames
        dealloc_frames(start_ppn, contiguous_count);
    }
    
    // Clean up all allocated frames
    for frame in allocated_frames {
        dealloc_frame(frame);
    }
    if let Some(ppn3) = frame3 {
        dealloc_frame(ppn3);
    }
    
    // Test 9: Post-cleanup allocation test
    test_count += 1;
    let final_frame = alloc_frame();
    if final_frame.is_none() {
        crate::println!("[FAIL] Test {}: Post-cleanup allocation failed", test_count);
        all_passed = false;
    } else {
        dealloc_frame(final_frame.unwrap());
    }
    
    if all_passed {
        crate::println!("[PASS] Bitmap frame allocator test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Bitmap frame allocator test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Test contiguous allocation functionality
pub fn test_contiguous_allocation() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    

    
    // Test 1: Small contiguous allocation
    test_count += 1;
    let small_count = 4;
    let small_frames = alloc_frames(small_count);
    if small_frames.is_none() {
        crate::println!("[FAIL] Test {}: Small contiguous allocation failed", test_count);
        all_passed = false;
    } else {
        let start_ppn = small_frames.unwrap();
        dealloc_frames(start_ppn, small_count);
    }
    
    // Test 2: Medium contiguous allocation
    test_count += 1;
    let medium_count = 16;
    let medium_frames = alloc_frames(medium_count);
    if medium_frames.is_none() {
        crate::println!("[FAIL] Test {}: Medium contiguous allocation failed", test_count);
        all_passed = false;
    } else {
        let start_ppn = medium_frames.unwrap();
        dealloc_frames(start_ppn, medium_count);
    }
    
    // Test 3: Large contiguous allocation
    test_count += 1;
    let large_count = 64;
    let large_frames = alloc_frames(large_count);
    if large_frames.is_none() {
        crate::println!("[WARN] Test {}: Large contiguous allocation failed (may be expected)", test_count);
        // Don't fail the test for this, as it might be expected
    } else {
        let start_ppn = large_frames.unwrap();
        dealloc_frames(start_ppn, large_count);
    }
    
    // Test 4: Zero allocation
    test_count += 1;
    let zero_frames = alloc_frames(0);
    if zero_frames.is_some() {
        crate::println!("[FAIL] Test {}: Zero frame allocation should return None", test_count);
        all_passed = false;
    }
    
    if all_passed {
        crate::println!("[PASS] Contiguous allocation test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Contiguous allocation test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Test frame allocator stress scenarios
pub fn test_frame_allocator_stress() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    

    
    // Test 1: Alternating allocation and deallocation
    test_count += 1;
    let mut frames = alloc::vec::Vec::new();
    for i in 0..100 {
        if let Some(frame) = alloc_frame() {
            frames.push(frame);
            
            // Deallocate every other frame
            if i % 2 == 1 && frames.len() >= 2 {
                let to_dealloc = frames.remove(frames.len() - 2);
                dealloc_frame(to_dealloc);
            }
        }
    }
    
    // Clean up remaining frames
    for frame in frames {
        dealloc_frame(frame);
    }
    
    // Test 2: Random allocation patterns
    test_count += 1;
    let mut allocated = alloc::vec::Vec::new();
    for i in 0..200 {
        if i % 3 == 0 && !allocated.is_empty() {
            // Deallocate a frame
            let frame = allocated.pop().unwrap();
            dealloc_frame(frame);
        } else {
            // Allocate a frame
            if let Some(frame) = alloc_frame() {
                allocated.push(frame);
            }
        }
    }
    
    // Clean up
    for frame in allocated {
        dealloc_frame(frame);
    }
    
    if all_passed {
        crate::println!("[PASS] Frame allocator stress test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Frame allocator stress test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Run all frame allocator tests
pub fn run_frame_tests() -> (usize, usize) {
    let mut passed = 0;
    let mut total = 0;
    
    crate::println!("=== Frame Allocator Tests ===");
    
    // Test bitmap frame allocator
    total += 1;
    if test_bitmap_frame_allocator() {
        passed += 1;
    }
    
    // Test contiguous allocation
    total += 1;
    if test_contiguous_allocation() {
        passed += 1;
    }
    
    // Test stress scenarios
    total += 1;
    if test_frame_allocator_stress() {
        passed += 1;
    }
    
    (passed, total)
}
