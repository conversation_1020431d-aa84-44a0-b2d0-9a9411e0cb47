//! Memory space and mapping area tests
//!
//! Comprehensive tests for MemorySpace and MapArea functionality

extern crate alloc;
use alloc::vec::Vec;
use alloc::vec;

use crate::mem::address::{PhysPageNum, VirtAddr, VirtPageNum};
use crate::mem::pagetable::CommonPTEFlags;
use crate::mem::memory_space::{MapArea, MapAreaType, MapItem, MemorySpace};

#[cfg(target_arch = "riscv64")]
use crate::arch::riscv64::pagetable::Rv64PageTable as ArchPageTable;

#[cfg(target_arch = "loongarch64")]
use crate::arch::loongarch64::pagetable::La64PageTable as ArchPageTable;

/// Test MapItem creation and basic operations
pub fn test_map_item() -> bool {
    crate::println!("Testing MapItem...");
    
    let vpn = VirtPageNum(0x1000);
    let ppn = PhysPageNum(0x2000);
    let flags = CommonPTEFlags::READABLE | CommonPTEFlags::WRITABLE | CommonPTEFlags::EXECUTABLE;
    
    let map_item = MapItem { vpn, ppn, flags };
    
    // Test basic properties
    if map_item.vpn != vpn {
        crate::println!("❌ MapItem VPN mismatch");
        return false;
    }
    
    if map_item.ppn != ppn {
        crate::println!("❌ MapItem PPN mismatch");
        return false;
    }
    
    if map_item.flags != flags {
        crate::println!("❌ MapItem flags mismatch");
        return false;
    }
    
    crate::println!("✅ MapItem test passed");
    true
}

/// Test MapArea creation and basic operations
pub fn test_map_area_basic() -> bool {
    crate::println!("Testing MapArea basic operations...");
    
    // Test identical mapping area
    let start_va = VirtAddr(0x1000);
    let end_va = VirtAddr(0x3000);
    let flags = CommonPTEFlags::READABLE | CommonPTEFlags::WRITABLE;
    
    let area = MapArea::new_identical(start_va, end_va, flags);
    
    // Should have 2 pages (0x1000-0x2000 and 0x2000-0x3000)
    if area.len() != 2 {
        crate::println!("❌ MapArea length mismatch, expected 2, got {}", area.len());
        return false;
    }
    
    if area.is_empty() {
        crate::println!("❌ MapArea should not be empty");
        return false;
    }
    
    // Test area type
    if area.area_type != MapAreaType::Identical {
        crate::println!("❌ MapArea type mismatch");
        return false;
    }
    
    // Test virtual address range
    if let Some((start, end)) = area.va_range() {
        if start != VirtAddr(0x1000) || end != VirtAddr(0x3000) {
            crate::println!("❌ MapArea VA range mismatch: {:?} - {:?}", start, end);
            return false;
        }
    } else {
        crate::println!("❌ MapArea VA range should not be None");
        return false;
    }
    
    crate::println!("✅ MapArea basic test passed");
    true
}

/// Test MapArea framed mapping
pub fn test_map_area_framed() -> bool {
    crate::println!("Testing MapArea framed mapping...");
    
    let start_va = VirtAddr(0x10000);
    let end_va = VirtAddr(0x12000);
    let flags = CommonPTEFlags::READABLE | CommonPTEFlags::WRITABLE | CommonPTEFlags::USER;
    
    let area = MapArea::new_framed(start_va, end_va, flags);
    
    // Should have 2 pages
    if area.len() != 2 {
        crate::println!("❌ Framed MapArea length mismatch, expected 2, got {}", area.len());
        return false;
    }
    
    // Test area type
    if area.area_type != MapAreaType::Framed {
        crate::println!("❌ Framed MapArea type mismatch");
        return false;
    }
    
    // All PPNs should be 0 (placeholder)
    for item in &area.map_items {
        if item.ppn.0 != 0 {
            crate::println!("❌ Framed MapArea should have placeholder PPNs");
            return false;
        }
        if item.flags != flags {
            crate::println!("❌ Framed MapArea flags mismatch");
            return false;
        }
    }
    
    crate::println!("✅ MapArea framed test passed");
    true
}

/// Test MapArea with custom items
pub fn test_map_area_custom() -> bool {
    crate::println!("Testing MapArea with custom items...");
    
    let mut map_items = Vec::new();
    map_items.push(MapItem {
        vpn: VirtPageNum(0x100),
        ppn: PhysPageNum(0x200),
        flags: CommonPTEFlags::READABLE | CommonPTEFlags::WRITABLE,
    });
    map_items.push(MapItem {
        vpn: VirtPageNum(0x101),
        ppn: PhysPageNum(0x201),
        flags: CommonPTEFlags::READABLE | CommonPTEFlags::EXECUTABLE,
    });
    
    let area = MapArea::new_with_items(map_items, MapAreaType::Linear { offset: 0x100 });
    
    if area.len() != 2 {
        crate::println!("❌ Custom MapArea length mismatch");
        return false;
    }
    
    if let MapAreaType::Linear { offset } = area.area_type {
        if offset != 0x100 {
            crate::println!("❌ Custom MapArea offset mismatch");
            return false;
        }
    } else {
        crate::println!("❌ Custom MapArea type mismatch");
        return false;
    }
    
    crate::println!("✅ MapArea custom test passed");
    true
}

/// Test MapArea mapping operations
pub fn test_map_area_operations() -> bool {
    crate::println!("Testing MapArea operations...");
    
    let mut area = MapArea::new_with_items(Vec::new(), MapAreaType::Framed);
    
    // Test adding mappings
    let vpn1 = VirtPageNum(0x100);
    let ppn1 = PhysPageNum(0x200);
    let flags1 = CommonPTEFlags::READABLE | CommonPTEFlags::WRITABLE;
    
    area.add_mapping(vpn1, ppn1, flags1);
    
    if area.len() != 1 {
        crate::println!("❌ MapArea should have 1 mapping after add");
        return false;
    }
    
    // Test finding mappings
    if let Some(item) = area.find_mapping(vpn1) {
        if item.vpn != vpn1 || item.ppn != ppn1 || item.flags != flags1 {
            crate::println!("❌ Found mapping data mismatch");
            return false;
        }
    } else {
        crate::println!("❌ Should find the added mapping");
        return false;
    }
    
    // Test finding non-existent mapping
    if area.find_mapping(VirtPageNum(0x999)).is_some() {
        crate::println!("❌ Should not find non-existent mapping");
        return false;
    }
    
    // Test removing mappings
    if !area.remove_mapping(vpn1) {
        crate::println!("❌ Should successfully remove existing mapping");
        return false;
    }
    
    if area.len() != 0 {
        crate::println!("❌ MapArea should be empty after removal");
        return false;
    }
    
    if area.remove_mapping(vpn1) {
        crate::println!("❌ Should not remove non-existent mapping");
        return false;
    }
    
    crate::println!("✅ MapArea operations test passed");
    true
}

/// Test MemorySpace creation and basic operations
pub fn test_memory_space_basic() -> bool {
    crate::println!("Testing MemorySpace basic operations...");
    
    let mut memory_space: MemorySpace<ArchPageTable> = MemorySpace::new();
    
    // Test initial state
    if memory_space.areas.len() != 0 {
        crate::println!("❌ New MemorySpace should have no areas");
        return false;
    }
    
    // Create a simple mapping area
    let start_va = VirtAddr(0x10000);
    let end_va = VirtAddr(0x11000);
    let flags = CommonPTEFlags::READABLE | CommonPTEFlags::WRITABLE | CommonPTEFlags::VALID;
    
    let area = MapArea::new_identical(start_va, end_va, flags);
    
    // Add the area to memory space
    if let Err(_) = memory_space.push(area) {
        crate::println!("❌ Failed to add area to MemorySpace");
        return false;
    }
    
    if memory_space.areas.len() != 1 {
        crate::println!("❌ MemorySpace should have 1 area after push");
        return false;
    }
    
    // Test token (should not be 0)
    let token = memory_space.token();
    if token == 0 {
        crate::println!("❌ MemorySpace token should not be 0");
        return false;
    }
    
    crate::println!("✅ MemorySpace basic test passed");
    true
}

/// Test MemorySpace area removal
pub fn test_memory_space_removal() -> bool {
    crate::println!("Testing MemorySpace area removal...");
    
    let mut memory_space: MemorySpace<ArchPageTable> = MemorySpace::new();
    
    // Add multiple areas
    let area1 = MapArea::new_identical(VirtAddr(0x10000), VirtAddr(0x11000), CommonPTEFlags::READABLE | CommonPTEFlags::WRITABLE | CommonPTEFlags::VALID);
    let area2 = MapArea::new_identical(VirtAddr(0x20000), VirtAddr(0x21000), CommonPTEFlags::READABLE | CommonPTEFlags::EXECUTABLE | CommonPTEFlags::VALID);
    let area3 = MapArea::new_identical(VirtAddr(0x30000), VirtAddr(0x31000), CommonPTEFlags::READABLE | CommonPTEFlags::VALID);
    
    if memory_space.push(area1).is_err() ||
       memory_space.push(area2).is_err() ||
       memory_space.push(area3).is_err() {
        crate::println!("❌ Failed to add areas to MemorySpace");
        return false;
    }
    
    if memory_space.areas.len() != 3 {
        crate::println!("❌ MemorySpace should have 3 areas");
        return false;
    }
    
    // Remove area that overlaps with the middle area (0x20000-0x21000)
    if let Err(_) = memory_space.remove(VirtAddr(0x20000), VirtAddr(0x21000)) {
        crate::println!("❌ Failed to remove area from MemorySpace");
        return false;
    }
    
    if memory_space.areas.len() != 2 {
        crate::println!("❌ MemorySpace should have 2 areas after removal");
        return false;
    }
    
    // Verify the correct area was removed by checking remaining areas
    let remaining_ranges: Vec<_> = memory_space.areas.iter()
        .filter_map(|area| area.va_range())
        .collect();
    
    let expected_ranges = vec![
        (VirtAddr(0x10000), VirtAddr(0x11000)),
        (VirtAddr(0x30000), VirtAddr(0x31000)),
    ];
    
    if remaining_ranges.len() != 2 {
        crate::println!("❌ Wrong number of remaining ranges");
        return false;
    }
    
    // Check if expected ranges are present (order might differ)
    for expected in &expected_ranges {
        if !remaining_ranges.contains(expected) {
            crate::println!("❌ Expected range {:?} not found in remaining ranges", expected);
            return false;
        }
    }
    
    crate::println!("✅ MemorySpace removal test passed");
    true
}

/// Test edge cases for MapArea and MemorySpace
pub fn test_memory_space_edge_cases() -> bool {
    crate::println!("Testing MemorySpace edge cases...");
    
    // Test empty MapArea
    let empty_area = MapArea::new_with_items(Vec::new(), MapAreaType::Framed);
    if !empty_area.is_empty() {
        crate::println!("❌ Empty MapArea should report as empty");
        return false;
    }
    
    if empty_area.va_range().is_some() {
        crate::println!("❌ Empty MapArea should have no VA range");
        return false;
    }
    
    // Test single page area
    let single_page_area = MapArea::new_identical(VirtAddr(0x1000), VirtAddr(0x2000), CommonPTEFlags::READABLE | CommonPTEFlags::VALID);
    if single_page_area.len() != 1 {
        crate::println!("❌ Single page area should have length 1");
        return false;
    }
    
    // Test large area
    let large_area = MapArea::new_identical(VirtAddr(0x100000), VirtAddr(0x200000), CommonPTEFlags::READABLE | CommonPTEFlags::VALID);
    let expected_pages = (0x200000 - 0x100000) / 0x1000; // 256 pages
    if large_area.len() != expected_pages {
        crate::println!("❌ Large area should have {} pages, got {}", expected_pages, large_area.len());
        return false;
    }
    
    crate::println!("✅ MemorySpace edge cases test passed");
    true
}

/// Run all memory space tests
pub fn run_memory_space_tests() -> (usize, usize) {
    let mut passed = 0;
    let mut total = 0;
    
    crate::println!("=== Memory Space and MapArea Tests ===");
    
    // Test MapItem
    total += 1;
    if test_map_item() {
        passed += 1;
    }
    
    // Test MapArea basic operations
    total += 1;
    if test_map_area_basic() {
        passed += 1;
    }
    
    // Test MapArea framed mapping
    total += 1;
    if test_map_area_framed() {
        passed += 1;
    }
    
    // Test MapArea with custom items
    total += 1;
    if test_map_area_custom() {
        passed += 1;
    }
    
    // Test MapArea operations
    total += 1;
    if test_map_area_operations() {
        passed += 1;
    }
    
    // Test MemorySpace basic operations
    total += 1;
    if test_memory_space_basic() {
        passed += 1;
    }
    
    // Test MemorySpace area removal
    total += 1;
    if test_memory_space_removal() {
        passed += 1;
    }
    
    // Test edge cases
    total += 1;
    if test_memory_space_edge_cases() {
        passed += 1;
    }
    
    crate::println!("Memory Space tests: {}/{} passed", passed, total);
    (passed, total)
}

/// Quick test for memory space functionality
pub fn test_memory_space_quick() -> bool {
    test_map_area_basic() && test_memory_space_basic()
}

/// Stress test for memory space operations
pub fn test_memory_space_stress() -> bool {
    crate::println!("Running MemorySpace stress test...");
    
    let mut memory_space: MemorySpace<ArchPageTable> = MemorySpace::new();
    
    // Add many small areas
    for i in 0..10 {
        let start_va = VirtAddr(0x100000 + i * 0x1000);
        let end_va = VirtAddr(0x100000 + (i + 1) * 0x1000);
        let area = MapArea::new_identical(start_va, end_va, CommonPTEFlags::READABLE | CommonPTEFlags::WRITABLE | CommonPTEFlags::VALID);
        
        if memory_space.push(area).is_err() {
            crate::println!("❌ Failed to add area {} in stress test", i);
            return false;
        }
    }
    
    if memory_space.areas.len() != 10 {
        crate::println!("❌ Stress test should have 10 areas");
        return false;
    }
    
    // Remove some areas
    if memory_space.remove(VirtAddr(0x102000), VirtAddr(0x105000)).is_err() {
        crate::println!("❌ Failed to remove areas in stress test");
        return false;
    }
    
    // Should have removed 3 areas (0x102000, 0x103000, 0x104000)
    if memory_space.areas.len() != 7 {
        crate::println!("❌ Stress test should have 7 areas after removal, got {}", memory_space.areas.len());
        return false;
    }
    
    crate::println!("✅ MemorySpace stress test passed");
    true
}
