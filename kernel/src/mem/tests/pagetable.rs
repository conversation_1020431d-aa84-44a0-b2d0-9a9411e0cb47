//! Page table comprehensive test module
//!
//! Tests for page table entries and page table operations

use crate::mem::address::PhysPageNum;
use crate::mem::pagetable::{PageTableEntry, PTEFlags};

/// Test page table entry operations with comprehensive coverage
pub fn test_page_table_entry() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    
    #[cfg(target_arch = "riscv64")]
    {
        use crate::arch::{Rv64PageTableEntry, Rv64PTEFlags};
        
        // Test 1: Basic PTE creation and properties
        test_count += 1;
        let ppn = PhysPageNum::new(0x1000);
        let flags = Rv64PTEFlags::VALID | Rv64PTEFlags::READABLE | Rv64PTEFlags::WRITABLE;
        let pte = Rv64PageTableEntry::new_page(ppn, flags);
        
        if !pte.is_valid() {
            crate::println!("[FAIL] Test {}: PTE should be valid", test_count);
            all_passed = false;
        }
        if !pte.is_readable() {
            crate::println!("[FAIL] Test {}: PTE should be readable", test_count);
            all_passed = false;
        }
        if !pte.is_writable() {
            crate::println!("[FAIL] Test {}: PTE should be writable", test_count);
            all_passed = false;
        }
        if pte.is_executable() {
            crate::println!("[FAIL] Test {}: PTE should not be executable", test_count);
            all_passed = false;
        }
        if pte.ppn() != ppn {
            crate::println!("[FAIL] Test {}: PTE ppn mismatch, expected {:?}, got {:?}", test_count, ppn, pte.ppn());
            all_passed = false;
        }
        
        // Test 2: Read-only PTE
        test_count += 1;
        let readonly_flags = Rv64PTEFlags::VALID | Rv64PTEFlags::READABLE;
        let readonly_pte = Rv64PageTableEntry::new_page(ppn, readonly_flags);
        
        if !readonly_pte.is_valid() {
            crate::println!("[FAIL] Test {}: Read-only PTE should be valid", test_count);
            all_passed = false;
        }
        if !readonly_pte.is_readable() {
            crate::println!("[FAIL] Test {}: Read-only PTE should be readable", test_count);
            all_passed = false;
        }
        if readonly_pte.is_writable() {
            crate::println!("[FAIL] Test {}: Read-only PTE should not be writable", test_count);
            all_passed = false;
        }
        if readonly_pte.is_executable() {
            crate::println!("[FAIL] Test {}: Read-only PTE should not be executable", test_count);
            all_passed = false;
        }
        
        // Test 3: Executable PTE
        test_count += 1;
        let exec_flags = Rv64PTEFlags::VALID | Rv64PTEFlags::READABLE | Rv64PTEFlags::EXECUTABLE;
        let exec_pte = Rv64PageTableEntry::new_page(ppn, exec_flags);
        
        if !exec_pte.is_valid() {
            crate::println!("[FAIL] Test {}: Executable PTE should be valid", test_count);
            all_passed = false;
        }
        if !exec_pte.is_readable() {
            crate::println!("[FAIL] Test {}: Executable PTE should be readable", test_count);
            all_passed = false;
        }
        if exec_pte.is_writable() {
            crate::println!("[FAIL] Test {}: Executable PTE should not be writable", test_count);
            all_passed = false;
        }
        if !exec_pte.is_executable() {
            crate::println!("[FAIL] Test {}: Executable PTE should be executable", test_count);
            all_passed = false;
        }
        
        // Test 4: Invalid PTE
        test_count += 1;
        let invalid_flags = Rv64PTEFlags::READABLE | Rv64PTEFlags::WRITABLE; // No VALID flag
        let invalid_pte = Rv64PageTableEntry::new_page(ppn, invalid_flags);
        
        if invalid_pte.is_valid() {
            crate::println!("[FAIL] Test {}: Invalid PTE should not be valid", test_count);
            all_passed = false;
        }
        
        // Test 5: User-accessible PTE
        test_count += 1;
        let user_flags = Rv64PTEFlags::VALID | Rv64PTEFlags::READABLE | Rv64PTEFlags::USER;
        let user_pte = Rv64PageTableEntry::new_page(ppn, user_flags);
        
        if !user_pte.is_valid() {
            crate::println!("[FAIL] Test {}: User PTE should be valid", test_count);
            all_passed = false;
        }
        if !user_pte.is_readable() {
            crate::println!("[FAIL] Test {}: User PTE should be readable", test_count);
            all_passed = false;
        }
        
        // Test 6: Zero PPN test
        test_count += 1;
        let zero_ppn = PhysPageNum::new(0);
        let zero_pte = Rv64PageTableEntry::new_page(zero_ppn, Rv64PTEFlags::VALID);
        
        if zero_pte.ppn() != zero_ppn {
            crate::println!("[FAIL] Test {}: Zero PPN test failed", test_count);
            all_passed = false;
        }
        
        // Test 7: Large PPN test
        test_count += 1;
        let large_ppn = PhysPageNum::new(0xFFFFF);
        let large_pte = Rv64PageTableEntry::new_page(large_ppn, Rv64PTEFlags::VALID);
        
        if large_pte.ppn() != large_ppn {
            crate::println!("[FAIL] Test {}: Large PPN test failed, expected {:?}, got {:?}", test_count, large_ppn, large_pte.ppn());
            all_passed = false;
        }
        
        // Test 8: Flag combination test
        test_count += 1;
        let all_flags = Rv64PTEFlags::VALID | Rv64PTEFlags::READABLE | Rv64PTEFlags::WRITABLE | 
                       Rv64PTEFlags::EXECUTABLE | Rv64PTEFlags::USER | Rv64PTEFlags::GLOBAL;
        let full_pte = Rv64PageTableEntry::new_page(ppn, all_flags);
        
        if !full_pte.is_valid() {
            crate::println!("[FAIL] Test {}: Full flags PTE should be valid", test_count);
            all_passed = false;
        }
        if !full_pte.is_readable() {
            crate::println!("[FAIL] Test {}: Full flags PTE should be readable", test_count);
            all_passed = false;
        }
        if !full_pte.is_writable() {
            crate::println!("[FAIL] Test {}: Full flags PTE should be writable", test_count);
            all_passed = false;
        }
        if !full_pte.is_executable() {
            crate::println!("[FAIL] Test {}: Full flags PTE should be executable", test_count);
            all_passed = false;
        }
        
        // Test 9: PTE flags extraction
        test_count += 1;
        let test_flags = Rv64PTEFlags::VALID | Rv64PTEFlags::READABLE;
        let flag_pte = Rv64PageTableEntry::new_page(ppn, test_flags);
        let extracted_flags = flag_pte.flags();
        
        // Basic flag verification (implementation-dependent)
        if extracted_flags.bits() == 0 {
            crate::println!("[FAIL] Test {}: Extracted flags should not be zero", test_count);
            all_passed = false;
        }
        
        // Test 10: Multiple PTE instances with different PPNs
        test_count += 1;
        let ppn_a = PhysPageNum::new(0x1000);
        let ppn_b = PhysPageNum::new(0x2000);
        let ppn_c = PhysPageNum::new(0x3000);
        
        let pte_a = Rv64PageTableEntry::new_page(ppn_a, Rv64PTEFlags::VALID);
        let pte_b = Rv64PageTableEntry::new_page(ppn_b, Rv64PTEFlags::VALID);
        let pte_c = Rv64PageTableEntry::new_page(ppn_c, Rv64PTEFlags::VALID);
        
        if pte_a.ppn() == pte_b.ppn() || pte_b.ppn() == pte_c.ppn() || pte_a.ppn() == pte_c.ppn() {
            crate::println!("[FAIL] Test {}: Different PTEs should have different PPNs", test_count);
            all_passed = false;
        }
        
        // Test 11: PTE bit manipulation edge cases
        test_count += 1;
        let edge_ppn = PhysPageNum::new(0x3FFFF); // Test near bit boundary
        let edge_pte = Rv64PageTableEntry::new_page(edge_ppn, Rv64PTEFlags::VALID | Rv64PTEFlags::READABLE);
        
        if edge_pte.ppn() != edge_ppn {
            crate::println!("[FAIL] Test {}: Edge case PPN failed, expected {:?}, got {:?}", test_count, edge_ppn, edge_pte.ppn());
            all_passed = false;
        }
        
        // Test 12: Write-only PTE (should be invalid in most architectures, but test anyway)
        test_count += 1;
        let writeonly_flags = Rv64PTEFlags::VALID | Rv64PTEFlags::WRITABLE; // No READABLE
        let writeonly_pte = Rv64PageTableEntry::new_page(ppn, writeonly_flags);
        
        if !writeonly_pte.is_valid() {
            crate::println!("[FAIL] Test {}: Write-only PTE should be valid (even if architecturally invalid)", test_count);
            all_passed = false;
        }
        if writeonly_pte.is_readable() {
            crate::println!("[FAIL] Test {}: Write-only PTE should not be readable", test_count);
            all_passed = false;
        }
        if !writeonly_pte.is_writable() {
            crate::println!("[FAIL] Test {}: Write-only PTE should be writable", test_count);
            all_passed = false;
        }
    }
    
    #[cfg(target_arch = "loongarch64")]
    {
        use crate::arch::{La64PageTableEntry, La64PTEFlags};
        
        // Test 1: Basic LoongArch64 PTE creation and properties
        test_count += 1;
        let ppn = PhysPageNum::new(0x1000);
        let flags = La64PTEFlags::VALID | La64PTEFlags::READABLE | La64PTEFlags::WRITABLE;
        let pte = La64PageTableEntry::new_page(ppn, flags);
        
        if !pte.is_valid() {
            crate::println!("[FAIL] Test {}: LoongArch64 PTE should be valid", test_count);
            all_passed = false;
        }
        if !pte.is_readable() {
            crate::println!("[FAIL] Test {}: LoongArch64 PTE should be readable", test_count);
            all_passed = false;
        }
        if !pte.is_writable() {
            crate::println!("[FAIL] Test {}: LoongArch64 PTE should be writable", test_count);
            all_passed = false;
        }
        if pte.ppn() != ppn {
            crate::println!("[FAIL] Test {}: LoongArch64 PTE ppn mismatch", test_count);
            all_passed = false;
        }
        
        // Test 2: LoongArch64 executable PTE
        test_count += 1;
        let exec_flags = La64PTEFlags::VALID | La64PTEFlags::READABLE | La64PTEFlags::EXECUTABLE;
        let exec_pte = La64PageTableEntry::new_page(ppn, exec_flags);
        
        if !exec_pte.is_executable() {
            crate::println!("[FAIL] Test {}: LoongArch64 executable PTE should be executable", test_count);
            all_passed = false;
        }
        
        // Test 3: LoongArch64 large PPN test
        test_count += 1;
        let large_ppn = PhysPageNum::new(0xFFFFF);
        let large_pte = La64PageTableEntry::new_page(large_ppn, La64PTEFlags::VALID);
        
        if large_pte.ppn() != large_ppn {
            crate::println!("[FAIL] Test {}: LoongArch64 large PPN test failed", test_count);
            all_passed = false;
        }
        
        // Test 4: LoongArch64 flag combination test
        test_count += 1;
        let all_flags = La64PTEFlags::VALID | La64PTEFlags::READABLE | La64PTEFlags::WRITABLE | 
                       La64PTEFlags::EXECUTABLE | La64PTEFlags::USER | La64PTEFlags::GLOBAL;
        let full_pte = La64PageTableEntry::new_page(ppn, all_flags);
        
        if !full_pte.is_valid() {
            crate::println!("[FAIL] Test {}: LoongArch64 full flags PTE should be valid", test_count);
            all_passed = false;
        }
        if !full_pte.is_readable() {
            crate::println!("[FAIL] Test {}: LoongArch64 full flags PTE should be readable", test_count);
            all_passed = false;
        }
        if !full_pte.is_writable() {
            crate::println!("[FAIL] Test {}: LoongArch64 full flags PTE should be writable", test_count);
            all_passed = false;
        }
        if !full_pte.is_executable() {
            crate::println!("[FAIL] Test {}: LoongArch64 full flags PTE should be executable", test_count);
            all_passed = false;
        }
    }
    
    if all_passed {
        crate::println!("[PASS] Page table entry test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Page table entry test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Test page table flags operations
pub fn test_pte_flags() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    
    #[cfg(target_arch = "riscv64")]
    {
        use crate::arch::Rv64PTEFlags;
        
        // Test 1: Flag combination with bitwise OR
        test_count += 1;
        let combined_flags = Rv64PTEFlags::VALID | Rv64PTEFlags::READABLE | Rv64PTEFlags::WRITABLE;
        if combined_flags.bits() == 0 {
            crate::println!("[FAIL] Test {}: Combined flags should not be zero", test_count);
            all_passed = false;
        }
        
        // Test 2: Flag contains check
        test_count += 1;
        if !combined_flags.contains(Rv64PTEFlags::VALID) {
            crate::println!("[FAIL] Test {}: Combined flags should contain VALID", test_count);
            all_passed = false;
        }
        if !combined_flags.contains(Rv64PTEFlags::READABLE) {
            crate::println!("[FAIL] Test {}: Combined flags should contain READABLE", test_count);
            all_passed = false;
        }
        
        // Test 3: Flag insertion
        test_count += 1;
        let mut mutable_flags = Rv64PTEFlags::VALID;
        mutable_flags |= Rv64PTEFlags::EXECUTABLE;
        if !mutable_flags.contains(Rv64PTEFlags::EXECUTABLE) {
            crate::println!("[FAIL] Test {}: Flags should contain EXECUTABLE after insertion", test_count);
            all_passed = false;
        }
    }
    
    #[cfg(target_arch = "loongarch64")]
    {
        use crate::arch::La64PTEFlags;
        
        // Test 1: LoongArch64 flag combination
        test_count += 1;
        let combined_flags = La64PTEFlags::VALID | La64PTEFlags::READABLE | La64PTEFlags::WRITABLE;
        if combined_flags.bits() == 0 {
            crate::println!("[FAIL] Test {}: LoongArch64 combined flags should not be zero", test_count);
            all_passed = false;
        }
        
        // Test 2: LoongArch64 flag contains check
        test_count += 1;
        if !combined_flags.contains(La64PTEFlags::VALID) {
            crate::println!("[FAIL] Test {}: LoongArch64 combined flags should contain VALID", test_count);
            all_passed = false;
        }
    }
    
    if all_passed {
        crate::println!("[PASS] PTE flags test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] PTE flags test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Test page table operations edge cases
pub fn test_pagetable_edge_cases() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    
    // Test 1: Maximum PPN values
    test_count += 1;
    let max_ppn = PhysPageNum::new(usize::MAX >> 12);
    
    #[cfg(target_arch = "riscv64")]
    {
        use crate::arch::{Rv64PageTableEntry, Rv64PTEFlags};
        let max_pte = Rv64PageTableEntry::new_page(max_ppn, Rv64PTEFlags::VALID);
        // Just test that it doesn't panic
        let _extracted_ppn = max_pte.ppn();
    }
    
    #[cfg(target_arch = "loongarch64")]
    {
        use crate::arch::{La64PageTableEntry, La64PTEFlags};
        let max_pte = La64PageTableEntry::new_page(max_ppn, La64PTEFlags::VALID);
        // Just test that it doesn't panic
        let _extracted_ppn = max_pte.ppn();
    }
    
    // Test 2: Minimum PPN values
    test_count += 1;
    let min_ppn = PhysPageNum::new(0);
    
    #[cfg(target_arch = "riscv64")]
    {
        use crate::arch::{Rv64PageTableEntry, Rv64PTEFlags};
        let min_pte = Rv64PageTableEntry::new_page(min_ppn, Rv64PTEFlags::VALID);
        if min_pte.ppn() != min_ppn {
            crate::println!("[FAIL] Test {}: Minimum PPN test failed", test_count);
            all_passed = false;
        }
    }
    
    #[cfg(target_arch = "loongarch64")]
    {
        use crate::arch::{La64PageTableEntry, La64PTEFlags};
        let min_pte = La64PageTableEntry::new_page(min_ppn, La64PTEFlags::VALID);
        if min_pte.ppn() != min_ppn {
            crate::println!("[FAIL] Test {}: LoongArch64 minimum PPN test failed", test_count);
            all_passed = false;
        }
    }
    
    if all_passed {
        crate::println!("[PASS] Page table edge cases test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Page table edge cases test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Run all page table tests
pub fn run_pagetable_tests() -> (usize, usize) {
    let mut passed = 0;
    let mut total = 0;
    
    crate::println!("=== Page Table Tests ===");
    
    // Test page table entries
    total += 1;
    if test_page_table_entry() {
        passed += 1;
    }
    
    // Test PTE flags
    total += 1;
    if test_pte_flags() {
        passed += 1;
    }
    
    // Test edge cases
    total += 1;
    if test_pagetable_edge_cases() {
        passed += 1;
    }
    
    (passed, total)
}
