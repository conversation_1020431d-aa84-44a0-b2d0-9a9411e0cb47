//! Heap memory comprehensive test module
//!
//! Tests for heap memory allocation and management

extern crate alloc;
use alloc::string::ToString;
use alloc::format;

/// Test basic heap memory allocation functionality
pub fn test_heap_basic() -> bool {
    // Test basic heap allocation using the heap module functions
    crate::mem::heap::test_heap_allocation()
}

/// Test advanced heap memory functionality
pub fn test_heap_advanced() -> bool {
    // Test heap stress allocation using the heap module functions
    crate::mem::heap::test_heap_stress()
}

/// Test heap memory edge cases and error conditions
pub fn test_heap_edge_cases() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    
    // Test 1: Large allocation attempt
    test_count += 1;
    {
        use alloc::vec::Vec;
        // Try to allocate a reasonably large vector
        let large_size = 64 * 1024; // 64KB of u8s
        let large_vec: Vec<u8> = Vec::with_capacity(large_size);
        if large_vec.capacity() >= large_size {
            crate::println!("[PASS] Test {}: Large allocation succeeded", test_count);
        } else {
            crate::println!("[WARN] Test {}: Large allocation capacity mismatch", test_count);
            // Don't fail the test for this
        }
    }
    
    // Test 2: Multiple small allocations
    test_count += 1;
    {
        use alloc::vec::Vec;
        let mut small_vecs = Vec::new();
        for i in 0..100 {
            let small_vec: Vec<u8> = Vec::with_capacity(64);
            small_vecs.push(small_vec);
            if i % 10 == 0 {
                // Periodically drop some allocations
                if small_vecs.len() > 5 {
                    small_vecs.remove(0);
                }
            }
        }
        crate::println!("[PASS] Test {}: Multiple small allocations completed", test_count);
    }
    
    // Test 3: String allocations
    test_count += 1;
    {
        use alloc::string::String;
        use alloc::vec::Vec;
        let mut strings = Vec::new();
        for i in 0..50usize {
            let s = String::from("Test string ") + &i.to_string();
            strings.push(s);
        }
        
        // Verify strings
        for (i, s) in strings.iter().enumerate() {
            let expected = String::from("Test string ") + &i.to_string();
            if *s != expected {
                crate::println!("[FAIL] Test {}: String content mismatch", test_count);
                all_passed = false;
                break;
            }
        }
        
        if all_passed {
            crate::println!("[PASS] Test {}: String allocations and verification completed", test_count);
        }
    }
    
    // Test 4: BTreeMap allocations
    test_count += 1;
    {
        use alloc::collections::BTreeMap;
        let mut map = BTreeMap::new();
        
        // Insert key-value pairs
        for i in 0..20 {
            map.insert(i, i * i);
        }
        
        // Verify contents
        for i in 0..20 {
            if map.get(&i) != Some(&(i * i)) {
                crate::println!("[FAIL] Test {}: BTreeMap content mismatch for key {}", test_count, i);
                all_passed = false;
                break;
            }
        }
        
        if all_passed {
            crate::println!("[PASS] Test {}: BTreeMap allocations and verification completed", test_count);
        }
    }
    
    // Test 5: Mixed allocation patterns
    test_count += 1;
    {
        use alloc::vec::Vec;
        use alloc::string::String;
        use alloc::boxed::Box;
        
        let mut mixed_allocs = Vec::new();
        
        // Mix different allocation types
        for i in 0..30usize {
            match i % 3 {
                0 => {
                    let boxed_val = Box::new(i);
                    mixed_allocs.push(format!("Box: {}", *boxed_val));
                }
                1 => {
                    let vec_val: Vec<usize> = (0..i).collect();
                    mixed_allocs.push(format!("Vec len: {}", vec_val.len()));
                }
                2 => {
                    let string_val = String::from("String ") + &i.to_string();
                    mixed_allocs.push(string_val);
                }
                _ => unreachable!(),
            }
        }
        
        if mixed_allocs.len() == 30 {
            crate::println!("[PASS] Test {}: Mixed allocation patterns completed", test_count);
        } else {
            crate::println!("[FAIL] Test {}: Mixed allocation patterns failed", test_count);
            all_passed = false;
        }
    }
    
    if all_passed {
        crate::println!("[PASS] Heap edge cases test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Heap edge cases test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Test heap memory fragmentation scenarios
pub fn test_heap_fragmentation() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    
    // Test 1: Alternating allocation and deallocation
    test_count += 1;
    {
        use alloc::vec::Vec;
        use alloc::boxed::Box;
        
        let mut allocations = Vec::new();
        
        // Allocate many small blocks
        for i in 0..100 {
            let boxed_val = Box::new(i);
            allocations.push(boxed_val);
        }
        
        // Deallocate every other block
        let mut i = 0;
        allocations.retain(|_| {
            let keep = i % 2 == 0;
            i += 1;
            keep
        });
        
        // Allocate more blocks (should reuse freed space)
        for i in 100..150 {
            let boxed_val = Box::new(i);
            allocations.push(boxed_val);
        }
        
        crate::println!("[PASS] Test {}: Fragmentation test completed with {} allocations", test_count, allocations.len());
    }
    
    // Test 2: Variable size allocations
    test_count += 1;
    {
        use alloc::vec::Vec;
        
        let mut variable_vecs = Vec::new();
        
        // Allocate vectors of different sizes
        for i in 1..=20 {
            let size = i * i; // 1, 4, 9, 16, 25, ...
            let vec: Vec<usize> = Vec::with_capacity(size);
            variable_vecs.push(vec);
        }
        
        // Remove some middle allocations
        variable_vecs.retain(|v| v.capacity() % 3 != 0);
        
        // Add more variable size allocations
        for i in 21..=30 {
            let size = i * 2;
            let vec: Vec<usize> = Vec::with_capacity(size);
            variable_vecs.push(vec);
        }
        
        crate::println!("[PASS] Test {}: Variable size allocation test completed", test_count);
    }
    
    if all_passed {
        crate::println!("[PASS] Heap fragmentation test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Heap fragmentation test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Test heap memory stress scenarios
pub fn test_heap_stress() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    
    // Test 1: Rapid allocation and deallocation
    test_count += 1;
    {
        use alloc::vec::Vec;
        
        for _ in 0..10 {
            let mut temp_vecs = Vec::new();
            
            // Rapidly allocate
            for i in 0..100 {
                let vec: Vec<u8> = Vec::with_capacity(i + 1);
                temp_vecs.push(vec);
            }
            
            // All vectors are automatically deallocated when temp_vecs goes out of scope
        }
        
        crate::println!("[PASS] Test {}: Rapid allocation/deallocation completed", test_count);
    }
    
    // Test 2: Nested allocations
    test_count += 1;
    {
        use alloc::vec::Vec;
        use alloc::string::String;
        
        let mut nested_data = Vec::new();
        
        for i in 0..20usize {
            let mut inner_vec = Vec::new();
            for j in 0..i {
                let s = String::from("Nested ") + &i.to_string() + "," + &j.to_string();
                inner_vec.push(s);
            }
            nested_data.push(inner_vec);
        }
        
        // Verify nested structure
        let total_strings: usize = nested_data.iter().map(|v| v.len()).sum();
        let expected_total = (0..20).sum::<usize>();
        
        if total_strings == expected_total {
            crate::println!("[PASS] Test {}: Nested allocations completed with {} strings", test_count, total_strings);
        } else {
            crate::println!("[FAIL] Test {}: Nested allocations failed, expected {}, got {}", test_count, expected_total, total_strings);
            all_passed = false;
        }
    }
    
    if all_passed {
        crate::println!("[PASS] Heap stress test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Heap stress test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Run all heap memory tests
pub fn run_heap_tests() -> (usize, usize) {
    let mut passed = 0;
    let mut total = 0;
    
    crate::println!("=== Heap Memory Tests ===");
    
    // Test basic heap functionality
    total += 1;
    if test_heap_basic() {
        passed += 1;
    }
    
    // Test advanced heap functionality
    total += 1;
    if test_heap_advanced() {
        passed += 1;
    }
    
    // Test heap edge cases
    total += 1;
    if test_heap_edge_cases() {
        passed += 1;
    }
    
    // Test heap fragmentation
    total += 1;
    if test_heap_fragmentation() {
        passed += 1;
    }
    
    // Test heap stress scenarios
    total += 1;
    if test_heap_stress() {
        passed += 1;
    }
    
    (passed, total)
}
