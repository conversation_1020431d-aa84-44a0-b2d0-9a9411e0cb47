//! Memory management comprehensive test suite
//!
//! This module contains comprehensive tests for all memory management components,
//! organized by functionality for better maintainability and coverage.

pub mod address;
pub mod frame;
pub mod pagetable;
pub mod heap;

/// Run all memory management tests with detailed reporting
pub fn run_all_tests() -> (usize, usize) {
    let mut total_passed = 0;
    let mut total_tests = 0;
    
    crate::println!("=== Comprehensive Memory Management Test Suite ===");
    crate::println!("");
    
    // Run address tests
    let (passed, total) = address::run_address_tests();
    total_passed += passed;
    total_tests += total;
    crate::println!("Address tests: {}/{} passed", passed, total);
    crate::println!("");
    
    // Run frame allocator tests
    let (passed, total) = frame::run_frame_tests();
    total_passed += passed;
    total_tests += total;
    crate::println!("Frame allocator tests: {}/{} passed", passed, total);
    crate::println!("");
    
    // Run page table tests
    let (passed, total) = pagetable::run_pagetable_tests();
    total_passed += passed;
    total_tests += total;
    crate::println!("Page table tests: {}/{} passed", passed, total);
    crate::println!("");
    
    // Run heap tests
    let (passed, total) = heap::run_heap_tests();
    total_passed += passed;
    total_tests += total;
    crate::println!("Heap memory tests: {}/{} passed", passed, total);
    crate::println!("");
    
    // Final summary
    crate::println!("=== Test Suite Summary ===");
    crate::println!("Total tests passed: {}/{}", total_passed, total_tests);
    
    if total_passed == total_tests {
        crate::println!("🎉 ALL TESTS PASSED! ✅");
    } else {
        crate::println!("❌ {} tests failed", total_tests - total_passed);
    }
    
    (total_passed, total_tests)
}

/// Run a quick subset of tests for fast verification
pub fn run_quick_tests() -> (usize, usize) {
    let mut total_passed = 0;
    let mut total_tests = 0;
    
    crate::println!("=== Quick Memory Management Tests ===");
    
    // Run basic address test
    total_tests += 1;
    if address::test_address_types() {
        total_passed += 1;
    }
    
    // Run basic frame allocator test
    total_tests += 1;
    if frame::test_bitmap_frame_allocator() {
        total_passed += 1;
    }
    
    // Run basic page table test
    total_tests += 1;
    if pagetable::test_page_table_entry() {
        total_passed += 1;
    }
    
    // Run basic heap test
    total_tests += 1;
    if heap::test_heap_basic() {
        total_passed += 1;
    }
    
    crate::println!("Quick tests: {}/{} passed", total_passed, total_tests);
    
    (total_passed, total_tests)
}

/// Run stress tests for performance and reliability verification
pub fn run_stress_tests() -> (usize, usize) {
    let mut total_passed = 0;
    let mut total_tests = 0;
    
    crate::println!("=== Memory Management Stress Tests ===");
    
    // Run frame allocator stress test
    total_tests += 1;
    if frame::test_frame_allocator_stress() {
        total_passed += 1;
    }
    
    // Run heap stress tests
    total_tests += 1;
    if heap::test_heap_stress() {
        total_passed += 1;
    }
    
    total_tests += 1;
    if heap::test_heap_fragmentation() {
        total_passed += 1;
    }
    
    // Run page table edge cases
    total_tests += 1;
    if pagetable::test_pagetable_edge_cases() {
        total_passed += 1;
    }
    
    // Run address edge cases
    total_tests += 1;
    if address::test_address_edge_cases() {
        total_passed += 1;
    }
    
    crate::println!("Stress tests: {}/{} passed", total_passed, total_tests);
    
    (total_passed, total_tests)
}

/// Legacy compatibility function - runs the comprehensive test suite
pub fn run_all_tests_with_heap() -> (usize, usize) {
    run_all_tests()
}
