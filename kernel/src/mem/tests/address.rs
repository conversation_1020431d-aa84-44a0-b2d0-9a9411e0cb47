//! Address types comprehensive test module
//!
//! Tests for PhysAddr, VirtAddr, PhysPageNum, VirtPageNum and related operations

use crate::mem::address::{PhysAddr, PhysPageNum, VirtAddr, VirtPageNum};

/// Test address type conversions and operations with comprehensive coverage
pub fn test_address_types() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    
    // Test 1: PhysAddr basic operations
    test_count += 1;
    let addr = PhysAddr::new(0x1000);
    if addr.to_ppn() != PhysPageNum::new(1) {
        crate::println!("[FAIL] Test {}: PhysAddr::to_ppn() failed", test_count);
        all_passed = false;
    }
    if addr.page_offset() != 0 {
        crate::println!("[FAIL] Test {}: PhysAddr::page_offset() failed for aligned address", test_count);
        all_passed = false;
    }
    if !addr.is_aligned() {
        crate::println!("[FAIL] Test {}: PhysAddr::is_aligned() failed for aligned address", test_count);
        all_passed = false;
    }
    
    // Test 2: PhysAddr unaligned operations
    test_count += 1;
    let addr2 = PhysAddr::new(0x1234);
    if addr2.to_ppn() != PhysPageNum::new(1) {
        crate::println!("[FAIL] Test {}: PhysAddr::to_ppn() failed for unaligned address", test_count);
        all_passed = false;
    }
    if addr2.page_offset() != 0x234 {
        crate::println!("[FAIL] Test {}: PhysAddr::page_offset() failed, expected 0x234, got 0x{:x}", test_count, addr2.page_offset());
        all_passed = false;
    }
    if addr2.is_aligned() {
        crate::println!("[FAIL] Test {}: PhysAddr::is_aligned() should be false for unaligned address", test_count);
        all_passed = false;
    }
    
    // Test 3: PhysPageNum operations
    test_count += 1;
    let ppn = PhysPageNum::new(1);
    if ppn.to_addr() != PhysAddr::new(0x1000) {
        crate::println!("[FAIL] Test {}: PhysPageNum::to_addr() failed", test_count);
        all_passed = false;
    }
    
    // Test 4: VirtAddr basic operations
    test_count += 1;
    let vaddr = VirtAddr::new(0x2000);
    if vaddr.to_vpn() != VirtPageNum::new(2) {
        crate::println!("[FAIL] Test {}: VirtAddr::to_vpn() failed", test_count);
        all_passed = false;
    }
    if vaddr.page_offset() != 0 {
        crate::println!("[FAIL] Test {}: VirtAddr::page_offset() failed for aligned address", test_count);
        all_passed = false;
    }
    if !vaddr.is_aligned() {
        crate::println!("[FAIL] Test {}: VirtAddr::is_aligned() failed for aligned address", test_count);
        all_passed = false;
    }
    
    // Test 5: VirtAddr unaligned operations
    test_count += 1;
    let vaddr2 = VirtAddr::new(0x2345);
    if vaddr2.to_vpn() != VirtPageNum::new(2) {
        crate::println!("[FAIL] Test {}: VirtAddr::to_vpn() failed for unaligned address", test_count);
        all_passed = false;
    }
    if vaddr2.page_offset() != 0x345 {
        crate::println!("[FAIL] Test {}: VirtAddr::page_offset() failed, expected 0x345, got 0x{:x}", test_count, vaddr2.page_offset());
        all_passed = false;
    }
    if vaddr2.is_aligned() {
        crate::println!("[FAIL] Test {}: VirtAddr::is_aligned() should be false for unaligned address", test_count);
        all_passed = false;
    }
    
    // Test 6: VirtPageNum operations
    test_count += 1;
    let vpn = VirtPageNum::new(2);
    if vpn.to_addr() != VirtAddr::new(0x2000) {
        crate::println!("[FAIL] Test {}: VirtPageNum::to_addr() failed", test_count);
        all_passed = false;
    }
    
    // Test 7: Arithmetic operations
    test_count += 1;
    let addr3 = PhysAddr::new(0x1000);
    let addr4 = addr3 + 0x100;
    if addr4 != PhysAddr::new(0x1100) {
        crate::println!("[FAIL] Test {}: PhysAddr addition failed", test_count);
        all_passed = false;
    }
    
    // Test 8: Page number arithmetic
    test_count += 1;
    let ppn2 = PhysPageNum::new(5);
    let ppn3 = PhysPageNum::new(ppn2.0 + 3);
    if ppn3 != PhysPageNum::new(8) {
        crate::println!("[FAIL] Test {}: PhysPageNum addition failed", test_count);
        all_passed = false;
    }
    
    // Test 9: Page number operations
    test_count += 1;
    let ppn_start = PhysPageNum::new(10);
    let ppn_next = PhysPageNum::new(ppn_start.0 + 5);
    if ppn_next != PhysPageNum::new(15) {
        crate::println!("[FAIL] Test {}: PhysPageNum arithmetic failed", test_count);
        all_passed = false;
    }
    
    // Test 10: Boundary conditions - zero addresses
    test_count += 1;
    let zero_addr = PhysAddr::new(0);
    if zero_addr.to_ppn() != PhysPageNum::new(0) {
        crate::println!("[FAIL] Test {}: Zero address to_ppn() failed", test_count);
        all_passed = false;
    }
    if zero_addr.page_offset() != 0 {
        crate::println!("[FAIL] Test {}: Zero address page_offset() failed", test_count);
        all_passed = false;
    }
    
    // Test 11: Large addresses
    test_count += 1;
    let large_addr = PhysAddr::new(0xFFFF_F000);
    if large_addr.to_ppn() != PhysPageNum::new(0xFFFF_F) {
        crate::println!("[FAIL] Test {}: Large address to_ppn() failed", test_count);
        all_passed = false;
    }
    if !large_addr.is_aligned() {
        crate::println!("[FAIL] Test {}: Large aligned address is_aligned() failed", test_count);
        all_passed = false;
    }
    
    // Test 12: Maximum page offset
    test_count += 1;
    let max_offset_addr = PhysAddr::new(0x1FFF);
    if max_offset_addr.page_offset() != 0xFFF {
        crate::println!("[FAIL] Test {}: Maximum page offset failed, expected 0xFFF, got 0x{:x}", test_count, max_offset_addr.page_offset());
        all_passed = false;
    }
    
    // Test 13: Virtual address range tests
    test_count += 1;
    let vaddr_high = VirtAddr::new(0x7FFF_FFFF_F000);
    if vaddr_high.to_vpn() != VirtPageNum::new(0x7FFF_FFFF_F) {
        crate::println!("[FAIL] Test {}: High virtual address to_vpn() failed", test_count);
        all_passed = false;
    }
    
    // Test 14: Address difference operations
    test_count += 1;
    let addr_a = PhysAddr::new(0x5000);
    let addr_b = PhysAddr::new(0x3000);
    if addr_a.0 - addr_b.0 != 0x2000 {
        crate::println!("[FAIL] Test {}: PhysAddr difference failed", test_count);
        all_passed = false;
    }
    
    // Test 15: Page number difference
    test_count += 1;
    let ppn_a = PhysPageNum::new(10);
    let ppn_b = PhysPageNum::new(7);
    if ppn_a.0 - ppn_b.0 != 3 {
        crate::println!("[FAIL] Test {}: PhysPageNum difference failed", test_count);
        all_passed = false;
    }
    
    if all_passed {
        crate::println!("[PASS] Address types test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Address types test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Test address conversion edge cases
pub fn test_address_edge_cases() -> bool {
    let mut all_passed = true;
    let mut test_count = 0;
    
    // Test 1: Address overflow behavior
    test_count += 1;
    let max_addr = PhysAddr::new(usize::MAX);
    // This should not panic, just test that it works
    let _ppn = max_addr.to_ppn();
    let _offset = max_addr.page_offset();
    
    // Test 2: Page number overflow
    test_count += 1;
    let max_ppn = PhysPageNum::new(usize::MAX >> 12);
    let _addr = max_ppn.to_addr();
    
    // Test 3: Virtual address sign extension (architecture dependent)
    test_count += 1;
    let high_vaddr = VirtAddr::new(0xFFFF_FFFF_FFFF_F000);
    let _vpn = high_vaddr.to_vpn();
    
    if all_passed {
        crate::println!("[PASS] Address edge cases test: All {} sub-tests passed", test_count);
    } else {
        crate::println!("[FAIL] Address edge cases test: Some tests failed out of {} total", test_count);
    }
    
    all_passed
}

/// Run all address-related tests
pub fn run_address_tests() -> (usize, usize) {
    let mut passed = 0;
    let mut total = 0;
    
    crate::println!("=== Address Type Tests ===");
    
    // Test address types
    total += 1;
    if test_address_types() {
        passed += 1;
    }
    
    // Test address edge cases
    total += 1;
    if test_address_edge_cases() {
        passed += 1;
    }
    
    (passed, total)
}
