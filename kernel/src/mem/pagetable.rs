//! Page table abstraction and management
//!
//! This module provides architecture-independent page table traits and interfaces.
//! Specific implementations are provided in each architecture's module.

use crate::mem::address::{PhysAddr, PhysPageNum, VirtAddr, VirtPageNum};
use core::fmt::Debug;

/// Page table entry flags
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub struct PTEFlags {
    bits: usize,
}

impl PTEFlags {
    /// Create empty flags
    pub const fn empty() -> Self {
        Self { bits: 0 }
    }
    
    /// Create flags from raw bits
    pub const fn from_bits(bits: usize) -> Self {
        Self { bits }
    }
    
    /// Get raw bits
    pub const fn bits(&self) -> usize {
        self.bits
    }
    
    /// Check if flags contain specific bits
    pub const fn contains(&self, other: Self) -> bool {
        (self.bits & other.bits) == other.bits
    }
    
    /// Insert flags
    pub fn insert(&mut self, other: Self) {
        self.bits |= other.bits;
    }
    
    /// Remove flags
    pub fn remove(&mut self, other: Self) {
        self.bits &= !other.bits;
    }
}

/// Implement BitOr for PTEFlags to support | operator
impl core::ops::BitOr for PTEFlags {
    type Output = Self;
    
    fn bitor(self, rhs: Self) -> Self::Output {
        Self { bits: self.bits | rhs.bits }
    }
}

/// Implement BitOrAssign for PTEFlags to support |= operator
impl core::ops::BitOrAssign for PTEFlags {
    fn bitor_assign(&mut self, rhs: Self) {
        self.bits |= rhs.bits;
    }
}

/// Common page table entry flags (architecture-specific values defined in arch modules)
pub struct CommonPTEFlags;

impl CommonPTEFlags {
    /// Valid/Present flag
    pub const VALID: PTEFlags = PTEFlags::from_bits(1 << 0);
    /// Readable flag
    pub const READABLE: PTEFlags = PTEFlags::from_bits(1 << 1);
    /// Writable flag
    pub const WRITABLE: PTEFlags = PTEFlags::from_bits(1 << 2);
    /// Executable flag
    pub const EXECUTABLE: PTEFlags = PTEFlags::from_bits(1 << 3);
    /// User accessible flag
    pub const USER: PTEFlags = PTEFlags::from_bits(1 << 4);
    /// Global flag
    pub const GLOBAL: PTEFlags = PTEFlags::from_bits(1 << 5);
    /// Accessed flag
    pub const ACCESSED: PTEFlags = PTEFlags::from_bits(1 << 6);
    /// Dirty flag
    pub const DIRTY: PTEFlags = PTEFlags::from_bits(1 << 7);
}

/// Page table entry trait
pub trait PageTableEntry: Debug + Clone + Copy + Sized {
    /// Create a new empty page table entry
    fn new() -> Self;
    
    /// Create a page table entry from physical page number and flags
    fn new_page(ppn: PhysPageNum, flags: PTEFlags) -> Self;
    
    /// Get the physical page number from this entry
    fn ppn(&self) -> PhysPageNum;
    
    /// Get the flags from this entry
    fn flags(&self) -> PTEFlags;
    
    /// Check if this entry is valid/present
    fn is_valid(&self) -> bool;
    
    /// Check if this entry is readable
    fn is_readable(&self) -> bool;
    
    /// Check if this entry is writable
    fn is_writable(&self) -> bool;
    
    /// Check if this entry is executable
    fn is_executable(&self) -> bool;
    
    /// Set flags for this entry
    fn set_flags(&mut self, flags: PTEFlags);
    
    /// Clear this entry
    fn clear(&mut self);
}

/// Page table trait
pub trait PageTable: Debug + Sized {
    /// Page table entry type for this architecture
    type Entry: PageTableEntry;
    
    /// Create a new empty page table
    fn new() -> Self;
    
    /// Create a page table from a root physical page
    fn from_token(token: usize) -> Self;
    
    /// Get the token (root page table physical address) for this page table
    fn token(&self) -> usize;
    
    /// Map a virtual page to a physical page with given flags
    fn map(&mut self, vpn: VirtPageNum, ppn: PhysPageNum, flags: PTEFlags) -> Result<(), PageTableError>;
    
    /// Unmap a virtual page
    fn unmap(&mut self, vpn: VirtPageNum) -> Result<(), PageTableError>;
    
    /// Translate a virtual page number to physical page number
    fn translate(&self, vpn: VirtPageNum) -> Option<PhysPageNum>;
    
    /// Translate a virtual address to physical address
    fn translate_va(&self, va: VirtAddr) -> Option<PhysAddr> {
        let vpn = va.floor();
        let offset = va.page_offset();
        self.translate(vpn).map(|ppn| ppn.into_pa() + offset)
    }
    
    /// Find or create a page table entry for the given virtual page
    fn find_pte_create(&mut self, vpn: VirtPageNum) -> Result<&mut Self::Entry, PageTableError>;
    
    /// Find a page table entry for the given virtual page (read-only)
    fn find_pte(&self, vpn: VirtPageNum) -> Option<&Self::Entry>;
    
    /// Get mutable reference to page table entry
    fn find_pte_mut(&mut self, vpn: VirtPageNum) -> Option<&mut Self::Entry>;
    
    /// Activate this page table (set as current page table)
    fn activate(&self);
    
    /// Flush TLB for a specific virtual address
    fn flush_tlb(&self, va: VirtAddr);
    
    /// Flush entire TLB
    fn flush_tlb_all(&self);
}

/// Page table operation errors
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PageTableError {
    /// Page not mapped
    NotMapped,
    /// Already mapped
    AlreadyMapped,
    /// Out of memory
    OutOfMemory,
    /// Invalid address
    InvalidAddress,
    /// Invalid virtual address
    InvalidVirtualAddress,
    /// Invalid physical address
    InvalidPhysicalAddress,
    /// Invalid flags
    InvalidFlags,
    /// Architecture-specific error
    ArchError(usize),
}

impl core::fmt::Display for PageTableError {
    fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
        match self {
            PageTableError::NotMapped => write!(f, "Page not mapped"),
            PageTableError::AlreadyMapped => write!(f, "Page already mapped"),
            PageTableError::OutOfMemory => write!(f, "Out of memory"),
            PageTableError::InvalidAddress => write!(f, "Invalid address"),
            PageTableError::InvalidVirtualAddress => write!(f, "Invalid virtual address"),
            PageTableError::InvalidPhysicalAddress => write!(f, "Invalid physical address"),
            PageTableError::InvalidFlags => write!(f, "Invalid flags"),
            PageTableError::ArchError(code) => write!(f, "Architecture error: {}", code),
        }
    }
}
