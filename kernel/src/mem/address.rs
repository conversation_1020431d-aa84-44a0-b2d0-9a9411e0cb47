//! Memory address types and utilities
//!
//! This module defines the basic address types used throughout the kernel:
//! - Physical addresses and page numbers
//! - Virtual addresses and page numbers
//! - Conversion utilities between addresses and page numbers

/// Page size in bytes (4KB)
pub const PAGE_SIZE: usize = 4096;

/// Page size shift (12 bits for 4KB pages)
pub const PAGE_SIZE_BITS: usize = 12;

/// Physical address wrapper
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct PhysAddr(pub usize);

impl PhysAddr {
    /// Create a new physical address
    pub const fn new(addr: usize) -> Self {
        Self(addr)
    }
    
    /// Get the raw address value
    pub const fn as_usize(self) -> usize {
        self.0
    }
    
    /// Convert to physical page number (floor division)
    pub const fn to_ppn(self) -> PhysPageNum {
        PhysPageNum(self.0 >> PAGE_SIZE_BITS)
    }
    
    /// Get page offset within the page
    pub const fn page_offset(self) -> usize {
        self.0 & (PAGE_SIZE - 1)
    }
    
    /// Check if address is page-aligned
    pub const fn is_aligned(self) -> bool {
        self.page_offset() == 0
    }
    
    /// Align address down to page boundary
    pub const fn align_down(self) -> Self {
        Self(self.0 & !(PAGE_SIZE - 1))
    }
    
    /// Align address up to page boundary
    pub const fn align_up(self) -> Self {
        Self((self.0 + PAGE_SIZE - 1) & !(PAGE_SIZE - 1))
    }
    
    /// Get as raw pointer
    pub fn as_ptr<T>(self) -> *const T {
        self.0 as *const T
    }
    
    /// Get as mutable raw pointer
    pub fn as_mut_ptr<T>(self) -> *mut T {
        self.0 as *mut T
    }
    
    /// Add offset to address
    pub const fn add(self, offset: usize) -> Self {
        Self(self.0 + offset)
    }
    
    /// Subtract offset from address
    pub const fn sub(self, offset: usize) -> Self {
        Self(self.0 - offset)
    }
}

impl core::fmt::Display for PhysAddr {
    fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
        write!(f, "PA:{:#x}", self.0)
    }
}

// Implement Add<usize> for PhysAddr to support offset calculations
impl core::ops::Add<usize> for PhysAddr {
    type Output = PhysAddr;
    
    fn add(self, offset: usize) -> Self::Output {
        PhysAddr(self.0 + offset)
    }
}

/// Physical page number
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct PhysPageNum(pub usize);

impl PhysPageNum {
    /// Create a new physical page number
    pub const fn new(ppn: usize) -> Self {
        Self(ppn)
    }
    
    /// Get the raw page number
    pub const fn as_usize(self) -> usize {
        self.0
    }
    
    /// Convert to physical address (start of page)
    pub const fn to_addr(self) -> PhysAddr {
        PhysAddr(self.0 << PAGE_SIZE_BITS)
    }
    
    /// Convert to physical address (alias for to_addr)
    pub const fn into_pa(self) -> PhysAddr {
        self.to_addr()
    }
    
    /// Get the next page number
    pub const fn next(self) -> Self {
        Self(self.0 + 1)
    }
    
    /// Get the previous page number
    pub const fn prev(self) -> Self {
        Self(self.0 - 1)
    }
    
    /// Add offset to page number
    pub const fn add(self, offset: usize) -> Self {
        Self(self.0 + offset)
    }
    
    /// Subtract offset from page number
    pub const fn sub(self, offset: usize) -> Self {
        Self(self.0 - offset)
    }
}

impl core::fmt::Display for PhysPageNum {
    fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
        write!(f, "PPN:{:#x}", self.0)
    }
}

// Implement From<usize> for PhysPageNum
impl From<usize> for PhysPageNum {
    fn from(ppn: usize) -> Self {
        Self(ppn)
    }
}



/// Virtual address wrapper
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct VirtAddr(pub usize);

impl VirtAddr {
    /// Create a new virtual address
    pub const fn new(addr: usize) -> Self {
        Self(addr)
    }
    
    /// Get the raw address value
    pub const fn as_usize(self) -> usize {
        self.0
    }
    
    /// Convert to virtual page number (floor division)
    pub const fn to_vpn(self) -> VirtPageNum {
        VirtPageNum(self.0 >> PAGE_SIZE_BITS)
    }
    
    /// Get virtual page number (floor division) - alias for to_vpn
    pub const fn floor(self) -> VirtPageNum {
        self.to_vpn()
    }
    
    /// Get virtual page number (ceiling division)
    pub const fn ceil(self) -> VirtPageNum {
        VirtPageNum((self.0 + PAGE_SIZE - 1) >> PAGE_SIZE_BITS)
    }
    
    /// Get page offset within the page
    pub const fn page_offset(self) -> usize {
        self.0 & (PAGE_SIZE - 1)
    }
    
    /// Check if address is page-aligned
    pub const fn is_aligned(self) -> bool {
        self.page_offset() == 0
    }
    
    /// Align address down to page boundary
    pub const fn align_down(self) -> Self {
        Self(self.0 & !(PAGE_SIZE - 1))
    }
    
    /// Align address up to page boundary
    pub const fn align_up(self) -> Self {
        Self((self.0 + PAGE_SIZE - 1) & !(PAGE_SIZE - 1))
    }
    
    /// Get as raw pointer
    pub fn as_ptr<T>(self) -> *const T {
        self.0 as *const T
    }
    
    /// Get as mutable raw pointer
    pub fn as_mut_ptr<T>(self) -> *mut T {
        self.0 as *mut T
    }
    
    /// Add offset to address
    pub const fn add(self, offset: usize) -> Self {
        Self(self.0 + offset)
    }
    
    /// Subtract offset from address
    pub const fn sub(self, offset: usize) -> Self {
        Self(self.0 - offset)
    }
}

impl core::fmt::Display for VirtAddr {
    fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
        write!(f, "VA:{:#x}", self.0)
    }
}

/// Virtual page number
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct VirtPageNum(pub usize);

impl VirtPageNum {
    /// Create a new virtual page number
    pub const fn new(vpn: usize) -> Self {
        Self(vpn)
    }
    
    /// Get the raw page number
    pub const fn as_usize(self) -> usize {
        self.0
    }
    
    /// Convert to virtual address (start of page)
    pub const fn to_addr(self) -> VirtAddr {
        VirtAddr(self.0 << PAGE_SIZE_BITS)
    }
    
    /// Get the next page number
    pub const fn next(self) -> Self {
        Self(self.0 + 1)
    }
    
    /// Get the previous page number
    pub const fn prev(self) -> Self {
        Self(self.0 - 1)
    }
    
    /// Add offset to page number
    pub const fn add(self, offset: usize) -> Self {
        Self(self.0 + offset)
    }
    
    /// Subtract offset from page number
    pub const fn sub(self, offset: usize) -> Self {
        Self(self.0 - offset)
    }
}

impl core::fmt::Display for VirtPageNum {
    fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
        write!(f, "VPN:{:#x}", self.0)
    }
}

// Implement From<usize> for VirtPageNum
impl From<usize> for VirtPageNum {
    fn from(vpn: usize) -> Self {
        Self(vpn)
    }
}



/// Address range utilities
pub struct AddrRange<T> {
    pub start: T,
    pub end: T,
}

impl<T: Copy + PartialOrd> AddrRange<T> {
    /// Create a new address range
    pub fn new(start: T, end: T) -> Self {
        Self { start, end }
    }
    
    /// Check if the range contains an address
    pub fn contains(&self, addr: T) -> bool {
        addr >= self.start && addr < self.end
    }
}

/// Physical address range
pub type PhysAddrRange = AddrRange<PhysAddr>;

/// Virtual address range
pub type VirtAddrRange = AddrRange<VirtAddr>;

/// Physical page number range
pub type PhysPageRange = AddrRange<PhysPageNum>;

/// Virtual page number range
pub type VirtPageRange = AddrRange<VirtPageNum>;

// Tests moved to mem/test.rs for no_std compatibility