//! Memory space and mapping area management
//!
//! This module provides memory space abstraction and mapping area management.
//! It works with the page table traits defined in the pagetable module.

extern crate alloc;
use alloc::vec::Vec;

use crate::mem::address::{PhysPageNum, VirtAddr, VirtPageNum};
use crate::mem::pagetable::{PageTable, PageTableError, PTEFlags};


#[derive(Debug, <PERSON>lone, Copy)]
pub struct MapItem
{
    pub vpn: VirtPageNum,
    pub ppn: PhysPageNum,
    pub flags: PTEFlags,
}


/// Memory mapping area
#[derive(Debug, Clone)]
pub struct MapArea {
    /// List of individual page mappings
    pub map_items: Vec<MapItem>,
    /// Area type (for reference, but actual mappings are in map_items)
    pub area_type: MapAreaType,
}

/// Memory mapping area type
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum MapAreaType {
    /// Identity mapping (virtual address == physical address)
    Identical,
    /// Framed mapping (allocated physical pages)
    Framed,
    /// Linear mapping (virtual address = physical address + offset)
    Linear { offset: isize },
}

impl MapArea {
    /// Create a new identical mapping area
    pub fn new_identical(start_va: VirtAddr, end_va: VirtAddr, flags: PTEFlags) -> Self {
        let start_vpn = start_va.floor();
        let end_vpn = end_va.ceil();
        let mut map_items = Vec::new();
        
        // Create MapItem for each page in the range
        let vpn_start = start_vpn.0;
        let vpn_end = end_vpn.0;
        for i in 0..(vpn_end - vpn_start) {
            let vpn = VirtPageNum(vpn_start + i);
            let ppn = PhysPageNum(vpn_start + i); // Identity mapping
            map_items.push(MapItem { vpn, ppn, flags });
        }
        
        Self {
            map_items,
            area_type: MapAreaType::Identical,
        }
    }
    
    /// Create a new framed mapping area (physical pages to be allocated later)
    pub fn new_framed(start_va: VirtAddr, end_va: VirtAddr, flags: PTEFlags) -> Self {
        let start_vpn = start_va.floor();
        let end_vpn = end_va.ceil();
        let mut map_items = Vec::new();
        
        // Create MapItem for each page in the range, PPNs will be set when allocated
        let vpn_start = start_vpn.0;
        let vpn_end = end_vpn.0;
        for i in 0..(vpn_end - vpn_start) {
            let vpn = VirtPageNum(vpn_start + i);
            let ppn = PhysPageNum(0); // Placeholder, will be set during allocation
            map_items.push(MapItem { vpn, ppn, flags });
        }
        
        Self {
            map_items,
            area_type: MapAreaType::Framed,
        }
    }
    
    /// Create a new mapping area with explicit MapItems
    pub fn new_with_items(map_items: Vec<MapItem>, area_type: MapAreaType) -> Self {
        Self {
            map_items,
            area_type,
        }
    }
    
    /// Map this area to a page table
    pub fn map_to<PT: PageTable>(&self, page_table: &mut PT) -> Result<(), PageTableError> {
        // Simply iterate through all MapItems and map each one
        for map_item in &self.map_items {
            page_table.map(map_item.vpn, map_item.ppn, map_item.flags)?;
        }
        Ok(())
    }
    
    /// Unmap this area from a page table
    pub fn unmap_from<PT: PageTable>(&self, page_table: &mut PT) -> Result<(), PageTableError> {
        // Simply iterate through all MapItems and unmap each one
        for map_item in &self.map_items {
            page_table.unmap(map_item.vpn)?;
        }
        Ok(())
    }
    
    /// Add a new mapping item to this area
    pub fn add_mapping(&mut self, vpn: VirtPageNum, ppn: PhysPageNum, flags: PTEFlags) {
        self.map_items.push(MapItem { vpn, ppn, flags });
    }
    
    /// Remove a mapping item by virtual page number
    pub fn remove_mapping(&mut self, vpn: VirtPageNum) -> bool {
        if let Some(pos) = self.map_items.iter().position(|item| item.vpn == vpn) {
            self.map_items.remove(pos);
            true
        } else {
            false
        }
    }
    
    /// Find a mapping item by virtual page number
    pub fn find_mapping(&self, vpn: VirtPageNum) -> Option<&MapItem> {
        self.map_items.iter().find(|item| item.vpn == vpn)
    }
    
    /// Get the number of mappings in this area
    pub fn len(&self) -> usize {
        self.map_items.len()
    }
    
    /// Check if this area is empty
    pub fn is_empty(&self) -> bool {
        self.map_items.is_empty()
    }
    
    /// Get virtual address range covered by this area
    pub fn va_range(&self) -> Option<(VirtAddr, VirtAddr)> {
        if self.map_items.is_empty() {
            return None;
        }
        
        let min_vpn = self.map_items.iter().map(|item| item.vpn.0).min().unwrap();
        let max_vpn = self.map_items.iter().map(|item| item.vpn.0).max().unwrap();
        
        Some((VirtAddr(min_vpn << 12), VirtAddr((max_vpn + 1) << 12)))
    }
}

/// Memory space (collection of mapping areas)
#[derive(Debug)]
pub struct MemorySpace<PT: PageTable> {
    /// Page table for this memory space
    pub page_table: PT,
    /// List of mapping areas
    pub areas: Vec<MapArea>,
}

impl<PT: PageTable> MemorySpace<PT> {
    /// Create a new memory space
    pub fn new() -> Self {
        Self {
            page_table: PT::new(),
            areas: Vec::new(),
        }
    }
    
    /// Add a mapping area
    pub fn push(&mut self, area: MapArea) -> Result<(), PageTableError> {
        area.map_to(&mut self.page_table)?;
        self.areas.push(area);
        Ok(())
    }
    
    /// Remove mapping areas that overlap with the given virtual address range
    pub fn remove(&mut self, start_va: VirtAddr, end_va: VirtAddr) -> Result<(), PageTableError> {
        let start_vpn = start_va.floor();
        let end_vpn = end_va.ceil();
        
        // Find and remove matching areas
        let mut i = 0;
        while i < self.areas.len() {
            let area = &self.areas[i];
            
            // Check if any mapping in this area overlaps with the range
            let has_overlap = area.map_items.iter().any(|item| {
                item.vpn.0 >= start_vpn.0 && item.vpn.0 < end_vpn.0
            });
            
            if has_overlap {
                let area = self.areas.remove(i);
                area.unmap_from(&mut self.page_table)?;
            } else {
                i += 1;
            }
        }
        
        Ok(())
    }
    
    /// Activate this memory space
    pub fn activate(&self) {
        self.page_table.activate();
    }
    
    /// Get the page table token
    pub fn token(&self) -> usize {
        self.page_table.token()
    }
    
    /// Translate virtual address to physical address
    pub fn translate(&self, va: VirtAddr) -> Option<crate::mem::address::PhysAddr> {
        self.page_table.translate_va(va)
    }
}
