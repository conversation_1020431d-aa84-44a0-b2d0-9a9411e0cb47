//! Heap memory management using buddy system allocator
//!
//! This module provides heap memory allocation services for the kernel using
//! the buddy system allocator algorithm.

extern crate alloc;

use buddy_system_allocator::LockedHeap;
use alloc::vec;

/// Heap size in bytes (1MB)
pub const HEAP_SIZE: usize = 1024 * 1024;

/// Static heap memory array
/// This array serves as the backing memory for our heap allocator
static mut HEAP_MEMORY: [u8; HEAP_SIZE] = [0; HEAP_SIZE];

/// Global heap allocator using buddy system
/// ORDER = 32 means we support allocations up to 2^32 bytes
#[global_allocator]
static HEAP_ALLOCATOR: LockedHeap<32> = LockedHeap::empty();

/// Heap statistics
#[derive(Debug, <PERSON>lone, Copy)]
pub struct HeapStats {
    pub total_size: usize,
    pub used_size: usize,
    pub free_size: usize,
    pub start_addr: usize,
}



/// Initialize the heap allocator
///
/// This function initializes the buddy system allocator with our static heap memory.
///
/// # Safety
/// This function should only be called once during kernel initialization.
pub unsafe fn init_heap() -> Result<(), &'static str> {
    // Get the address of our static heap memory array
    let heap_start = unsafe { core::ptr::addr_of!(HEAP_MEMORY) as *const u8 as usize };
    let heap_end = heap_start + HEAP_SIZE;
    
    // Initialize the heap allocator with the static memory
    unsafe {
        HEAP_ALLOCATOR.lock().add_to_heap(heap_start, heap_end);
    }
    
    Ok(())
}

/// Get heap statistics
pub fn heap_stats() -> HeapStats {
    let total_size = HEAP_SIZE;
    let used_size = 0; // buddy_system_allocator doesn't provide this info directly
    let free_size = total_size - used_size;
    let start_addr = unsafe { core::ptr::addr_of!(HEAP_MEMORY) as *const u8 as usize };
    
    HeapStats {
        total_size,
        used_size,
        free_size,
        start_addr,
    }
}

/// Test heap allocation functionality
pub fn test_heap_allocation() -> bool {
    use alloc::vec::Vec;
    use alloc::boxed::Box;
    
    // Test basic allocation
    let test_box = Box::new(42u32);
    if *test_box != 42 {
        return false;
    }
    
    // Test vector allocation
    let mut test_vec = Vec::new();
    for i in 0..100 {
        test_vec.push(i);
    }
    
    if test_vec.len() != 100 {
        return false;
    }
    
    // Verify vector contents
    for (i, &value) in test_vec.iter().enumerate() {
        if value != i {
            return false;
        }
    }
    
    // Test large allocation
    let large_vec: Vec<u8> = vec![0; 1024];
    if large_vec.len() != 1024 {
        return false;
    }
    
    // Test string allocation
    let test_string = alloc::string::String::from("Hello, heap!");
    if test_string != "Hello, heap!" {
        return false;
    }
    
    true
}

/// Advanced heap allocation test
pub fn test_heap_stress() -> bool {
    use alloc::vec::Vec;
    use alloc::collections::BTreeMap;
    
    // Test multiple allocations and deallocations
    let mut vectors = Vec::new();
    
    // Allocate multiple vectors
    for i in 0..10 {
        let mut vec = Vec::new();
        for j in 0..i * 10 {
            vec.push(j);
        }
        vectors.push(vec);
    }
    
    // Verify all vectors
    for (i, vec) in vectors.iter().enumerate() {
        if vec.len() != i * 10 {
            return false;
        }
        for (j, &value) in vec.iter().enumerate() {
            if value != j {
                return false;
            }
        }
    }
    
    // Test BTreeMap (more complex data structure)
    let mut map = BTreeMap::new();
    for i in 0..50 {
        map.insert(i, i * 2);
    }
    
    if map.len() != 50 {
        return false;
    }
    
    for (key, value) in map.iter() {
        if *value != *key * 2 {
            return false;
        }
    }
    
    true
}

/// Display heap information
/// Note: This function is a placeholder since we're in a no_std environment
/// The actual printing should be handled by the caller using appropriate logging mechanisms
pub fn print_heap_info() {
    // In a no_std environment, we can't use println! directly
    // The caller should use the heap_stats() function to get the information
    // and handle printing through their own logging system
}

/// Cleanup heap (reset heap memory)
///
/// # Safety
/// This function should only be called during kernel shutdown.
/// Since we use a static array, we just reset the memory to zero.
pub unsafe fn cleanup_heap() {
    // Reset the static heap memory to zero
    unsafe {
        let heap_ptr = core::ptr::addr_of_mut!(HEAP_MEMORY) as *mut [u8; HEAP_SIZE];
        (*heap_ptr).fill(0);
    }
    
    // Note: We don't need to deallocate anything since we're using a static array
    // The memory will be automatically cleaned up when the kernel shuts down
}

#[cfg(test)]
mod tests {
    use super::*;
    use alloc::vec::Vec;
    use alloc::boxed::Box;
    
    #[test]
    fn test_basic_allocation() {
        let test_box = Box::new(123);
        assert_eq!(*test_box, 123);
        
        let mut test_vec = Vec::new();
        test_vec.push(1);
        test_vec.push(2);
        test_vec.push(3);
        assert_eq!(test_vec, vec![1, 2, 3]);
    }
    
    #[test]
    fn test_large_allocation() {
        let large_vec: Vec<u32> = vec![42; 1000];
        assert_eq!(large_vec.len(), 1000);
        assert!(large_vec.iter().all(|&x| x == 42));
    }
}