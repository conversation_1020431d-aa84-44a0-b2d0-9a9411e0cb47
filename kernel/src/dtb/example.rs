//! DTB usage examples

use super::*;

/// Example function showing how to use DTB information
pub fn dtb_usage_example() {
    crate::println!("\n=== DTB Usage Example ===");
    
    // Get the complete DTB information
    let dtb_info = get_dtb_info();
    
    // Print summary using the built-in method
    dtb_info.print_summary();
    
    // Access specific information
    crate::println!("\n--- Accessing Specific Information ---");
    
    // Get main memory information
    let (mem_start, mem_size) = get_main_memory();
    crate::println!("Main Memory: 0x{:x} + 0x{:x} bytes", mem_start, mem_size);
    crate::println!("Main Memory: {} MB", mem_size / (1024 * 1024));
    
    // Get CPU information
    let cpu_count = get_cpu_count();
    crate::println!("CPU Count: {}", cpu_count);
    
    // Get hart IDs
    let hart_ids = get_hart_ids();
    if !hart_ids.is_empty() {
        crate::print!("Hart IDs: ");
        for (i, hart_id) in hart_ids.iter().enumerate() {
            if i > 0 { crate::print!(", "); }
            crate::print!("{}", hart_id);
        }
        crate::println!("");
    }
    
    // Get all memory regions
    let memory_regions = get_memory_regions();
    crate::println!("Memory Regions: {}", memory_regions.len());
    for (i, (base, size)) in memory_regions.iter().enumerate() {
        crate::println!("  Region {}: 0x{:x} - 0x{:x} ({} MB)", 
                       i, base, base + size, size / (1024 * 1024));
    }
    
    // Calculate total memory
    let total_memory = dtb_info.get_total_memory();
    crate::println!("Total Memory: {} MB", total_memory / (1024 * 1024));
    
    // Check for multiple memory regions
    if dtb_info.has_multiple_memory_regions() {
        crate::println!("System has multiple memory regions");
    } else {
        crate::println!("System has a single memory region");
    }
    
    // Access model and compatible information
    if let Some(ref model) = dtb_info.model {
        crate::println!("System Model: {}", model);
    }
    
    if let Some(ref compatible) = dtb_info.compatible {
        crate::println!("Compatible: {}", compatible);
    }
    
    crate::println!("========================");
}

/// Example function for memory management integration
pub fn memory_management_example() {
    crate::println!("\n=== Memory Management Integration Example ===");
    
    let dtb_info = get_dtb_info();
    
    // Example: Use DTB information for memory allocator setup
    let (main_mem_start, main_mem_size) = dtb_info.get_main_memory();
    
    crate::println!("Setting up memory allocator with DTB information:");
    crate::println!("  Base Address: 0x{:x}", main_mem_start);
    crate::println!("  Size: 0x{:x} ({} MB)", main_mem_size, main_mem_size / (1024 * 1024));
    
    // Calculate usable memory ranges (example)
    let kernel_end = 0x80400000u64; // Example kernel end address
    let usable_start = if main_mem_start < kernel_end {
        kernel_end
    } else {
        main_mem_start
    };
    
    let usable_size = main_mem_start + main_mem_size - usable_start;
    
    crate::println!("  Usable Memory Start: 0x{:x}", usable_start);
    crate::println!("  Usable Memory Size: 0x{:x} ({} MB)", usable_size, usable_size / (1024 * 1024));
    
    // Handle multiple memory regions
    if dtb_info.has_multiple_memory_regions() {
        crate::println!("Additional memory regions available:");
        for (i, (base, size)) in dtb_info.memory_regions.iter().enumerate().skip(1) {
            crate::println!("  Region {}: 0x{:x} + 0x{:x} ({} MB)", 
                           i, base, size, size / (1024 * 1024));
        }
    }
    
    crate::println!("===========================================");
}

/// Example function for CPU management integration
pub fn cpu_management_example() {
    crate::println!("\n=== CPU Management Integration Example ===");
    
    let dtb_info = get_dtb_info();
    
    crate::println!("CPU Configuration from DTB:");
    crate::println!("  CPU Count: {}", dtb_info.cpu_count);
    
    if !dtb_info.hart_ids.is_empty() {
        crate::println!("  Hart IDs: {:?}", dtb_info.hart_ids);
        
        // Example: Initialize per-CPU data structures
        crate::println!("Initializing per-CPU data structures:");
        for hart_id in &dtb_info.hart_ids {
            crate::println!("  Initializing CPU {}", hart_id);
            // Here you would initialize per-CPU data structures
        }
        
        // Example: Set up SMP (Symmetric Multi-Processing)
        if dtb_info.cpu_count > 1 {
            crate::println!("Multi-processor system detected");
            crate::println!("  Primary Hart: {}", dtb_info.hart_ids[0]);
            crate::println!("  Secondary Harts: {:?}", &dtb_info.hart_ids[1..]);
        } else {
            crate::println!("Single-processor system");
        }
    }
    
    crate::println!("========================================");
}

/// Run all DTB usage examples
pub fn run_dtb_examples() {
    dtb_usage_example();
    memory_management_example();
    cpu_management_example();
}
