//! DTB parser tests

use super::*;
use crate::mem::address::PhysAddr;

/// Test DTB parser initialization with invalid address
pub fn test_dtb_invalid_address() -> bool {
    let mut parser = DtbParser::new();
    
    // Test with null address
    let result = unsafe { parser.init(PhysAddr::new(0)) };
    match result {
        Err("Invalid DTB address: null pointer") => true,
        _ => false,
    }
}

/// Test DTB parser with mock DTB data
pub fn test_dtb_mock_data() -> bool {
    // Create a minimal mock DTB header with correct magic number
    let mock_dtb = [
        0xd0, 0x0d, 0xfe, 0xed, // Magic number (big-endian)
        0x00, 0x00, 0x00, 0x40, // Total size: 64 bytes (big-endian)
        // Add more mock data as needed...
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    ];
    
    let mut parser = DtbParser::new();
    let mock_addr = PhysAddr::new(mock_dtb.as_ptr() as usize);
    
    // This might fail because the mock DTB is not a complete valid DTB,
    // but it should at least pass the magic number check
    let result = unsafe { parser.init(mock_addr) };
    
    // For now, we just check that it doesn't panic and returns some result
    true
}

/// Test DTB parser with invalid magic number
pub fn test_dtb_invalid_magic() -> bool {
    // Create mock data with invalid magic number
    let mock_dtb = [
        0x12, 0x34, 0x56, 0x78, // Invalid magic number
        0x00, 0x00, 0x00, 0x40, // Total size: 64 bytes
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    ];
    
    let mut parser = DtbParser::new();
    let mock_addr = PhysAddr::new(mock_dtb.as_ptr() as usize);
    
    let result = unsafe { parser.init(mock_addr) };
    match result {
        Err("Invalid DTB magic number") => true,
        _ => false,
    }
}

/// Test DTB info structure
pub fn test_dtb_info_structure() -> bool {
    let mut info = DtbInfo::new();

    // Test default values
    if info.mem_start != 0 || info.mem_size != 0 || info.cpu_count != 0 {
        return false;
    }

    // Test setting values
    info.mem_start = 0x80000000;
    info.mem_size = 0x8000000; // 128MB
    info.cpu_count = 4;
    info.memory_regions.push((0x80000000, 0x8000000));
    info.hart_ids.push(0);
    info.hart_ids.push(1);

    // Test getter methods
    let (main_start, main_size) = info.get_main_memory();
    if main_start != 0x80000000 || main_size != 0x8000000 {
        return false;
    }

    let total_memory = info.get_total_memory();
    if total_memory != 0x8000000 {
        return false;
    }

    if info.has_multiple_memory_regions() {
        return false; // Should be false with only one region
    }

    // Add another region
    info.memory_regions.push((0x90000000, 0x4000000));
    if !info.has_multiple_memory_regions() {
        return false; // Should be true now
    }

    true
}

/// Test DTB convenience functions
pub fn test_dtb_convenience_functions() -> bool {
    // These functions require a properly initialized DTB parser
    // For now, just test that they don't panic
    let _memory_regions = get_memory_regions();
    let _cpu_count = get_cpu_count();
    let _hart_ids = get_hart_ids();
    let _main_memory = get_main_memory();
    let _dtb_info = get_dtb_info();

    true
}

/// Run all DTB tests
pub fn run_dtb_tests() -> (usize, usize) {
    let mut passed = 0;
    let mut total = 0;

    crate::println!("\n=== DTB Parser Tests ===");

    // Test 1: Invalid address
    total += 1;
    if test_dtb_invalid_address() {
        crate::println!("[PASS] DTB invalid address test");
        passed += 1;
    } else {
        crate::println!("[FAIL] DTB invalid address test");
    }

    // Test 2: Invalid magic number
    total += 1;
    if test_dtb_invalid_magic() {
        crate::println!("[PASS] DTB invalid magic test");
        passed += 1;
    } else {
        crate::println!("[FAIL] DTB invalid magic test");
    }

    // Test 3: Mock data (basic functionality)
    total += 1;
    if test_dtb_mock_data() {
        crate::println!("[PASS] DTB mock data test");
        passed += 1;
    } else {
        crate::println!("[FAIL] DTB mock data test");
    }

    // Test 4: DTB info structure
    total += 1;
    if test_dtb_info_structure() {
        crate::println!("[PASS] DTB info structure test");
        passed += 1;
    } else {
        crate::println!("[FAIL] DTB info structure test");
    }

    // Test 5: Convenience functions
    total += 1;
    if test_dtb_convenience_functions() {
        crate::println!("[PASS] DTB convenience functions test");
        passed += 1;
    } else {
        crate::println!("[FAIL] DTB convenience functions test");
    }

    crate::println!("DTB Tests: {}/{} passed", passed, total);

    (passed, total)
}
