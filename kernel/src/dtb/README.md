# DTB (Device Tree Blob) Parser

这个模块为 Echos 内核提供了 DTB 解析功能，主要用于 RISC-V64 架构。

## 功能特性

- **DTB 解析**: 解析从 bootloader 传递的 Device Tree Blob
- **结构化信息存储**: 使用 `DtbInfo` 结构存储解析后的硬件信息
- **内存信息提取**: 从 DTB 中提取系统内存布局信息
- **CPU 信息提取**: 获取 CPU 数量和配置信息
- **设备信息**: 解析设备树中的硬件设备信息
- **便利函数**: 提供简单的 API 访问常用信息
- **安全性**: 使用 Mutex 保护全局 DTB 解析器实例

## 使用方法

### 1. 初始化 DTB 解析器

在内核启动时，DTB 地址会通过 RISC-V 的 a1 寄存器传递给内核：

```rust
// 在 rust_main 函数中
#[cfg(target_arch = "riscv64")]
{
    let dtb_addr = boot::riscv64::get_dtb_address();
    unsafe {
        match dtb::init_dtb(dtb_addr) {
            Ok(()) => {
                println!("[PASS] DTB parser initialization successful");
                dtb::print_dtb_info();
            }
            Err(e) => {
                println!("[WARN] DTB parser initialization failed: {}", e);
            }
        }
    }
}
```

### 2. 获取系统信息

#### 使用便利函数（推荐）

```rust
// 获取主内存信息
let (mem_start, mem_size) = dtb::get_main_memory();
println!("Main Memory: 0x{:x} + 0x{:x} bytes", mem_start, mem_size);

// 获取 CPU 数量
let cpu_count = dtb::get_cpu_count();
println!("CPU count: {}", cpu_count);

// 获取所有内存区域
let memory_regions = dtb::get_memory_regions();
for (base, size) in memory_regions {
    println!("Memory: base=0x{:x}, size=0x{:x}", base, size);
}

// 获取 Hart IDs
let hart_ids = dtb::get_hart_ids();
println!("Hart IDs: {:?}", hart_ids);

// 获取完整的 DTB 信息结构
let dtb_info = dtb::get_dtb_info();
println!("Total memory: {} MB", dtb_info.get_total_memory() / (1024 * 1024));
```

#### 使用 DTB 解析器实例

```rust
// 获取 DTB 解析器实例
let parser = dtb::get_dtb_parser();
let parser_guard = parser.lock();

// 获取 DTB 信息结构
let dtb_info = parser_guard.get_info();

// 访问具体信息
if let Some(ref model) = dtb_info.model {
    println!("System model: {}", model);
}

let (main_start, main_size) = dtb_info.get_main_memory();
println!("Main memory: 0x{:x} + 0x{:x}", main_start, main_size);
```

### 3. 打印 DTB 信息

```rust
// 打印完整的 DTB 信息（详细版本）
dtb::print_dtb_info();

// 打印 DTB 信息摘要（推荐）
dtb::print_dtb_summary();
```

## DTB 信息结构

### DtbInfo 结构

```rust
pub struct DtbInfo {
    /// 主内存起始地址
    pub mem_start: u64,
    /// 主内存大小（字节）
    pub mem_size: u64,
    /// CPU 数量
    pub cpu_count: usize,
    /// 系统型号名称
    pub model: Option<String>,
    /// 兼容性字符串
    pub compatible: Option<String>,
    /// 所有内存区域 (基地址, 大小)
    pub memory_regions: Vec<(u64, u64)>,
    /// CPU Hart ID 列表
    pub hart_ids: Vec<u32>,
}
```

### 便利方法

```rust
// 获取主内存区域（最大的一个）
let (start, size) = dtb_info.get_main_memory();

// 获取所有内存区域的总大小
let total = dtb_info.get_total_memory();

// 检查是否有多个内存区域
if dtb_info.has_multiple_memory_regions() {
    println!("System has multiple memory regions");
}

// 打印信息摘要
dtb_info.print_summary();
```

## 架构支持

目前主要支持 RISC-V64 架构：

- **RISC-V64**: 完整支持，DTB 地址通过 a1 寄存器传递
- **LoongArch64**: 暂不支持（LoongArch 通常使用 ACPI 而不是 DTB）

## 依赖

- `fdt = "0.1.5"`: FDT (Flattened Device Tree) 解析库
- `spin`: 用于 Mutex 同步
- `alloc`: 用于动态内存分配（Vec 等）

## 安全性考虑

- DTB 地址验证：检查地址是否为空
- Magic number 验证：验证 DTB 的魔数 (0xd00dfeed)
- 大小检查：验证 DTB 大小的合理性
- Unsafe 操作：所有不安全的内存操作都被适当封装

## 测试

模块包含了完整的测试套件：

```rust
// 运行 DTB 解析器测试
let (passed, total) = dtb::tests::run_dtb_tests();
```

测试包括：
- 无效地址测试
- 无效魔数测试
- 模拟数据测试

## 错误处理

DTB 解析器会返回详细的错误信息：

- `"Invalid DTB address: null pointer"`: DTB 地址为空
- `"DTB too small to contain header"`: DTB 数据太小
- `"Invalid DTB magic number"`: DTB 魔数不正确
- `"Invalid DTB size"`: DTB 大小不合理
- `"Failed to parse DTB"`: FDT 解析失败

## 示例输出

```
=== Device Tree Information ===
Root node found
Model: QEMU RISC-V Virt machine
Compatible: riscv-virtio

--- Memory Information ---
Memory: base=0x80000000, size=0x8000000 (128 MB)

--- CPU Information ---
CPU 0: riscv
  Hart ID: 0
Total CPUs: 1
==============================
```

## 未来扩展

- 支持更多设备类型的解析
- 添加设备驱动程序的自动发现
- 支持 DTB 的动态修改
- 添加更多架构的支持
