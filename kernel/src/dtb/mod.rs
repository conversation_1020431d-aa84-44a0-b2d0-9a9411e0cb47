extern crate alloc;

use fdt::Fdt;
use crate::mem::address::PhysAddr;
use alloc::vec::Vec;
use alloc::string::{String, ToString};
use alloc::format;
use spin::Mutex;

pub mod tests;
pub mod example;

/// Device information structure
#[derive(Debug, Clone)]
pub struct DeviceInfo {
    /// Device name
    pub name: String,
    /// Compatible string
    pub compatible: Option<String>,
    /// Register addresses (base, size)
    pub registers: Vec<(u64, u64)>,
    /// Interrupt numbers
    pub interrupts: Vec<u32>,
    /// Device status
    pub status: Option<String>,
    /// Device path in device tree
    pub path: String,
}

impl DeviceInfo {
    pub fn new(name: String, path: String) -> Self {
        Self {
            name,
            compatible: None,
            registers: Vec::new(),
            interrupts: Vec::new(),
            status: None,
            path,
        }
    }
}

/// DTB information structure to store parsed hardware information
#[derive(Debug, Clone)]
pub struct DtbInfo {
    /// Memory start address
    pub mem_start: u64,
    /// Memory size in bytes
    pub mem_size: u64,
    /// Number of CPUs
    pub cpu_count: usize,
    /// System model name
    pub model: Option<String>,
    /// Compatible string
    pub compatible: Option<String>,
    /// All memory regions (base, size)
    pub memory_regions: Vec<(u64, u64)>,
    /// CPU hart IDs
    pub hart_ids: Vec<u32>,
    /// Detected devices
    pub devices: Vec<DeviceInfo>,
}

impl Default for DtbInfo {
    fn default() -> Self {
        Self {
            mem_start: 0,
            mem_size: 0,
            cpu_count: 0,
            model: None,
            compatible: None,
            memory_regions: Vec::new(),
            hart_ids: Vec::new(),
            devices: Vec::new(),
        }
    }
}

impl DtbInfo {
    /// Create a new empty DTB info structure
    pub fn new() -> Self {
        Self::default()
    }

    /// Get the main memory region (largest one)
    pub fn get_main_memory(&self) -> (u64, u64) {
        (self.mem_start, self.mem_size)
    }

    /// Get total memory size across all regions
    pub fn get_total_memory(&self) -> u64 {
        self.memory_regions.iter().map(|(_, size)| size).sum()
    }

    /// Check if the system has multiple memory regions
    pub fn has_multiple_memory_regions(&self) -> bool {
        self.memory_regions.len() > 1
    }

    /// Print DTB information summary
    pub fn print_summary(&self) {
        crate::dtblog!("=== DTB Information Summary ===");

        if let Some(ref model) = self.model {
            crate::dtblog!("Model: {}", model);
        }

        if let Some(ref compatible) = self.compatible {
            crate::dtblog!("Compatible: {}", compatible);
        }

        crate::dtblog!("Main Memory: 0x{:x} - 0x{:x} ({} MB)",
                       self.mem_start,
                       self.mem_start + self.mem_size,
                       self.mem_size / (1024 * 1024));

        if self.has_multiple_memory_regions() {
            crate::dtblog!("Additional memory regions: {}", self.memory_regions.len() - 1);
            for (i, (base, size)) in self.memory_regions.iter().enumerate().skip(1) {
                crate::dtblog!("  Region {}: 0x{:x} - 0x{:x} ({} MB)",
                               i, base, base + size, size / (1024 * 1024));
            }
        }

        crate::dtblog!("Total Memory: {} MB", self.get_total_memory() / (1024 * 1024));
        crate::dtblog!("CPU Count: {}", self.cpu_count);

        if !self.hart_ids.is_empty() {
            crate::print!("Hart IDs: ");
            for (i, hart_id) in self.hart_ids.iter().enumerate() {
                if i > 0 { crate::print!(", "); }
                crate::print!("{}", hart_id);
            }
            crate::println!("");
        }

        crate::dtblog!("===============================");
    }
}

/// DTB (Device Tree Blob) parser for the kernel
pub struct DtbParser {
    fdt: Option<Fdt<'static>>,
    info: DtbInfo,
}

impl DtbParser {
    /// Create a new DTB parser
    pub fn new() -> Self {
        Self {
            fdt: None,
            info: DtbInfo::new(),
        }
    }

    /// Initialize the DTB parser with the device tree blob
    ///
    /// # Arguments
    /// * `dtb_addr` - Physical address of the DTB in memory
    ///
    /// # Safety
    /// The caller must ensure that the DTB address points to valid DTB data
    pub unsafe fn init(&mut self, dtb_addr: PhysAddr) -> Result<(), &'static str> {
        if dtb_addr.as_usize() == 0 {
            return Err("Invalid DTB address: null pointer");
        }

        // Convert physical address to virtual address
        let dtb_virt_addr = {
            #[cfg(target_arch = "riscv64")]
            {
                // RISC-V64: In QEMU, DTB is typically accessible through direct mapping
                // The bootloader maps it to a virtual address that we can access
                // For now, try direct access - if this fails, we need proper mapping
                dtb_addr.as_usize()
            }
            #[cfg(target_arch = "loongarch64")]
            {
                // LoongArch64: DTB is at 0x100000, convert to virtual address
                dtb_addr.as_usize() + 0x9000000000000000
            }
        };

        crate::dtblog!("DTB physical address: 0x{:x}, virtual address: 0x{:x}",
                       dtb_addr.as_usize(), dtb_virt_addr);

        let dtb_ptr = dtb_virt_addr as *const u8;
        
        // Create a slice from the DTB data
        // First, read the DTB header to get the total size
        let header_slice = unsafe { core::slice::from_raw_parts(dtb_ptr, 8) };
        if header_slice.len() < 8 {
            return Err("DTB too small to contain header");
        }

        // DTB magic number should be 0xd00dfeed (big-endian)
        let magic = u32::from_be_bytes([header_slice[0], header_slice[1], header_slice[2], header_slice[3]]);
        if magic != 0xd00dfeed {
            return Err("Invalid DTB magic number");
        }

        // Get total size from DTB header (offset 4, big-endian)
        let total_size = u32::from_be_bytes([header_slice[4], header_slice[5], header_slice[6], header_slice[7]]) as usize;
        
        if total_size < 8 || total_size > 0x100000 { // Sanity check: max 1MB
            return Err("Invalid DTB size");
        }

        // Create slice for the entire DTB
        let dtb_slice = unsafe { core::slice::from_raw_parts(dtb_ptr, total_size) };
        
        // Parse the FDT
        match Fdt::new(dtb_slice) {
            Ok(fdt) => {
                self.fdt = Some(fdt);
                // Parse and store DTB information
                self.parse_dtb_info();
                Ok(())
            }
            Err(_) => Err("Failed to parse DTB")
        }
    }

    /// Parse DTB information and store it in the info structure
    fn parse_dtb_info(&mut self) {
        if let Some(fdt) = self.fdt.as_ref() {
            // Reset info
            self.info = DtbInfo::new();

            // Parse root node information
            if let Some(root) = fdt.find_node("/") {
                // Get model
                if let Some(model) = root.property("model") {
                    if let Some(model_str) = model.as_str() {
                        self.info.model = Some(model_str.to_string());
                    }
                }

                // Get compatible
                if let Some(compatible) = root.property("compatible") {
                    if let Some(compatible_str) = compatible.as_str() {
                        self.info.compatible = Some(compatible_str.to_string());
                    }
                }
            }

            // Parse memory information
            for node in fdt.find_all_nodes("/memory") {
                if let Some(reg) = node.property("reg") {
                    let reg_data = reg.value;
                    if reg_data.len() >= 16 {
                        let base = u64::from_be_bytes([
                            reg_data[0], reg_data[1], reg_data[2], reg_data[3],
                            reg_data[4], reg_data[5], reg_data[6], reg_data[7]
                        ]);
                        let size = u64::from_be_bytes([
                            reg_data[8], reg_data[9], reg_data[10], reg_data[11],
                            reg_data[12], reg_data[13], reg_data[14], reg_data[15]
                        ]);

                        self.info.memory_regions.push((base, size));

                        // Set main memory to the first (usually largest) region
                        if self.info.mem_start == 0 {
                            self.info.mem_start = base;
                            self.info.mem_size = size;
                        }
                    }
                }
            }

            // Parse CPU information
            if let Some(cpus_node) = fdt.find_node("/cpus") {
                for cpu in cpus_node.children() {
                    if cpu.name.starts_with("cpu") {
                        self.info.cpu_count += 1;

                        // Get hart ID
                        if let Some(reg) = cpu.property("reg") {
                            let reg_data = reg.value;
                            if reg_data.len() >= 4 {
                                let hart_id = u32::from_be_bytes([
                                    reg_data[0], reg_data[1], reg_data[2], reg_data[3]
                                ]);
                                self.info.hart_ids.push(hart_id);
                            }
                        }
                    }
                }
            }

            // Parse device information
            self.parse_devices_from_fdt();
        }
    }

    /// Parse device information from DTB
    fn parse_devices_from_fdt(&mut self) {
        // Clear existing devices
        self.info.devices.clear();

        // We need to avoid borrowing self.fdt while also borrowing self mutably
        // So we'll collect the device information first, then store it
        let mut devices = Vec::new();

        if let Some(fdt) = self.fdt.as_ref() {
            if let Some(root) = fdt.find_node("/") {
                Self::collect_node_devices_recursive(root, "/", &mut devices);
            }
        }

        self.info.devices = devices;
    }

    /// Static method to collect device information recursively
    fn collect_node_devices_recursive(node: fdt::node::FdtNode, path: &str, devices: &mut Vec<DeviceInfo>) {
        // Skip certain nodes that are not devices
        let skip_nodes = ["memory", "cpus", "chosen", "aliases"];
        if skip_nodes.contains(&node.name) || node.name.starts_with("cpu") {
            // Still parse children for these nodes
            for child in node.children() {
                let child_path = if path == "/" {
                    format!("/{}", child.name)
                } else {
                    format!("{}/{}", path, child.name)
                };
                Self::collect_node_devices_recursive(child, &child_path, devices);
            }
            return;
        }

        // Check if this node represents a device (has compatible property or reg property)
        let has_compatible = node.property("compatible").is_some();
        let has_reg = node.property("reg").is_some();

        if has_compatible || has_reg {
            let mut device = DeviceInfo::new(node.name.to_string(), path.to_string());

            // Get compatible string
            if let Some(compatible) = node.property("compatible") {
                if let Some(compatible_str) = compatible.as_str() {
                    device.compatible = Some(compatible_str.to_string());
                }
            }

            // Get register information
            if let Some(reg) = node.property("reg") {
                let reg_data = reg.value;
                let mut i = 0;
                while i + 15 < reg_data.len() {
                    let base = u64::from_be_bytes([
                        reg_data[i], reg_data[i+1], reg_data[i+2], reg_data[i+3],
                        reg_data[i+4], reg_data[i+5], reg_data[i+6], reg_data[i+7]
                    ]);
                    let size = u64::from_be_bytes([
                        reg_data[i+8], reg_data[i+9], reg_data[i+10], reg_data[i+11],
                        reg_data[i+12], reg_data[i+13], reg_data[i+14], reg_data[i+15]
                    ]);
                    device.registers.push((base, size));
                    i += 16;
                }
            }

            // Get interrupt information
            if let Some(interrupts) = node.property("interrupts") {
                let int_data = interrupts.value;
                let mut i = 0;
                while i + 3 < int_data.len() {
                    let interrupt = u32::from_be_bytes([
                        int_data[i], int_data[i+1], int_data[i+2], int_data[i+3]
                    ]);
                    device.interrupts.push(interrupt);
                    i += 4;
                }
            }

            // Get status
            if let Some(status) = node.property("status") {
                if let Some(status_str) = status.as_str() {
                    device.status = Some(status_str.to_string());
                }
            }

            devices.push(device);
        }

        // Parse children recursively
        for child in node.children() {
            let child_path = if path == "/" {
                format!("/{}", child.name)
            } else {
                format!("{}/{}", path, child.name)
            };
            Self::collect_node_devices_recursive(child, &child_path, devices);
        }
    }



    /// Get the FDT reference
    pub fn fdt(&self) -> Option<&Fdt> {
        self.fdt.as_ref()
    }

    /// Get the parsed DTB information
    pub fn get_info(&self) -> &DtbInfo {
        &self.info
    }

    /// Print basic DTB information
    pub fn print_info(&self) {
        if let Some(fdt) = &self.fdt {
            crate::dtblog!("=== Device Tree Information ===");

            // Print root node information
            if let Some(root) = fdt.find_node("/") {
                crate::dtblog!("Root node found");

                // Print model if available
                if let Some(model) = root.property("model") {
                    if let Some(model_str) = model.as_str() {
                        crate::dtblog!("Model: {}", model_str);
                    }
                }

                // Print compatible if available
                if let Some(compatible) = root.property("compatible") {
                    if let Some(compatible_str) = compatible.as_str() {
                        crate::dtblog!("Compatible: {}", compatible_str);
                    }
                }
            }

            // Print memory information
            self.print_memory_info();

            // Print CPU information
            self.print_cpu_info();

            // Print device information
            self.print_device_info();

            crate::dtblog!("==============================");
        } else {
            crate::dtblog!("No DTB loaded");
        }
    }

    /// Print memory information from DTB
    pub fn print_memory_info(&self) {
        if let Some(fdt) = &self.fdt {
            crate::dtblog!("\n--- Memory Information ---");

            for node in fdt.find_all_nodes("/memory") {
                if let Some(reg) = node.property("reg") {
                    let reg_data = reg.value;
                    if reg_data.len() >= 16 { // Assuming 64-bit addresses
                        let base = u64::from_be_bytes([
                            reg_data[0], reg_data[1], reg_data[2], reg_data[3],
                            reg_data[4], reg_data[5], reg_data[6], reg_data[7]
                        ]);
                        let size = u64::from_be_bytes([
                            reg_data[8], reg_data[9], reg_data[10], reg_data[11],
                            reg_data[12], reg_data[13], reg_data[14], reg_data[15]
                        ]);
                        crate::dtblog!("Memory: base=0x{:x}, size=0x{:x} ({} MB)",
                                base, size, size / (1024 * 1024));
                    }
                }
            }
        }
    }

    /// Print CPU information from DTB
    pub fn print_cpu_info(&self) {
        if let Some(fdt) = &self.fdt {
            crate::dtblog!("\n--- CPU Information ---");

            if let Some(cpus_node) = fdt.find_node("/cpus") {
                let mut cpu_count = 0;

                for cpu in cpus_node.children() {
                    if cpu.name.starts_with("cpu") {
                        cpu_count += 1;

                        if let Some(compatible) = cpu.property("compatible") {
                            if let Some(compatible_str) = compatible.as_str() {
                                crate::dtblog!("CPU {}: {}", cpu_count - 1, compatible_str);
                            }
                        }

                        if let Some(reg) = cpu.property("reg") {
                            let reg_data = reg.value;
                            if reg_data.len() >= 4 {
                                let hart_id = u32::from_be_bytes([
                                    reg_data[0], reg_data[1], reg_data[2], reg_data[3]
                                ]);
                                crate::dtblog!("  Hart ID: {}", hart_id);
                            }
                        }
                    }
                }

                if cpu_count > 0 {
                    crate::dtblog!("Total CPUs: {}", cpu_count);
                }
            }
        }
    }

    /// Print device information from DTB
    pub fn print_device_info(&self) {
        crate::dtblog!("\n--- Device Information ---");

        if self.info.devices.is_empty() {
            crate::dtblog!("No devices found in DTB");
            return;
        }

        crate::dtblog!("Found {} devices:", self.info.devices.len());

        for (i, device) in self.info.devices.iter().enumerate() {
            crate::dtblog!("\nDevice {}: {}", i + 1, device.name);
            crate::dtblog!("  Path: {}", device.path);

            if let Some(ref compatible) = device.compatible {
                crate::dtblog!("  Compatible: {}", compatible);
            }

            if let Some(ref status) = device.status {
                crate::dtblog!("  Status: {}", status);
            }

            if !device.registers.is_empty() {
                crate::dtblog!("  Registers:");
                for (j, (base, size)) in device.registers.iter().enumerate() {
                    crate::dtblog!("    Reg {}: 0x{:x} - 0x{:x} (size: 0x{:x})",
                                   j, base, base + size, size);
                }
            }

            if !device.interrupts.is_empty() {
                crate::dtblog!("  Interrupts: {:?}", device.interrupts);
            }
        }
    }

    /// Get memory regions from DTB
    pub fn get_memory_regions(&self) -> Vec<(u64, u64)> {
        let mut regions = Vec::new();
        
        if let Some(fdt) = &self.fdt {
            for node in fdt.find_all_nodes("/memory") {
                if let Some(reg) = node.property("reg") {
                    let reg_data = reg.value;
                    if reg_data.len() >= 16 {
                        let base = u64::from_be_bytes([
                            reg_data[0], reg_data[1], reg_data[2], reg_data[3],
                            reg_data[4], reg_data[5], reg_data[6], reg_data[7]
                        ]);
                        let size = u64::from_be_bytes([
                            reg_data[8], reg_data[9], reg_data[10], reg_data[11],
                            reg_data[12], reg_data[13], reg_data[14], reg_data[15]
                        ]);
                        regions.push((base, size));
                    }
                }
            }
        }
        
        regions
    }

    /// Get the number of CPUs from DTB
    pub fn get_cpu_count(&self) -> usize {
        if let Some(fdt) = &self.fdt {
            if let Some(cpus_node) = fdt.find_node("/cpus") {
                return cpus_node.children()
                    .filter(|cpu| cpu.name.starts_with("cpu"))
                    .count();
            }
        }
        0
    }
}

/// Global DTB parser instance
static DTB_PARSER: Mutex<DtbParser> = Mutex::new(DtbParser {
    fdt: None,
    info: DtbInfo {
        mem_start: 0,
        mem_size: 0,
        cpu_count: 0,
        model: None,
        compatible: None,
        memory_regions: Vec::new(),
        hart_ids: Vec::new(),
        devices: Vec::new(),
    }
});

/// Initialize the global DTB parser
///
/// # Safety
/// This function should only be called once during kernel initialization
pub unsafe fn init_dtb(dtb_addr: PhysAddr) -> Result<(), &'static str> {
    unsafe { DTB_PARSER.lock().init(dtb_addr) }
}

/// Get a reference to the global DTB parser
pub fn get_dtb_parser() -> &'static Mutex<DtbParser> {
    &DTB_PARSER
}

/// Print DTB information using the global parser
pub fn print_dtb_info() {
    get_dtb_parser().lock().print_info();
}

/// Get DTB information from the global parser
pub fn get_dtb_info() -> DtbInfo {
    get_dtb_parser().lock().get_info().clone()
}

/// Get main memory information from DTB
pub fn get_main_memory() -> (u64, u64) {
    let parser = get_dtb_parser().lock();
    let info = parser.get_info();
    (info.mem_start, info.mem_size)
}

/// Get CPU count from DTB
pub fn get_cpu_count() -> usize {
    get_dtb_parser().lock().get_info().cpu_count
}

/// Get all memory regions from DTB
pub fn get_memory_regions() -> Vec<(u64, u64)> {
    get_dtb_parser().lock().get_info().memory_regions.clone()
}

/// Get hart IDs from DTB
pub fn get_hart_ids() -> Vec<u32> {
    get_dtb_parser().lock().get_info().hart_ids.clone()
}

/// Print DTB information summary
pub fn print_dtb_summary() {
    get_dtb_parser().lock().get_info().print_summary();
}

/// Get device information from DTB
pub fn get_devices() -> Vec<DeviceInfo> {
    get_dtb_parser().lock().get_info().devices.clone()
}

/// Print device information from DTB
pub fn print_device_info() {
    get_dtb_parser().lock().print_device_info();
}
