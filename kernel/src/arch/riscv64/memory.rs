//! RISC-V memory detection and management
//!
//! This module provides functionality to detect available memory regions
//! from the device tree blob (DTB) passed by OpenSBI.

extern crate alloc;

use crate::mem::address::{PhysAddr, PhysPageNum};
use crate::println;
use alloc::vec::Vec;

/// Memory region information
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct MemoryRegion {
    pub start: PhysAddr,
    pub size: usize,
}

impl MemoryRegion {
    pub fn new(start: PhysAddr, size: usize) -> Self {
        Self { start, size }
    }

    pub fn end(&self) -> PhysAddr {
        PhysAddr::new(self.start.as_usize() + self.size)
    }

    pub fn start_ppn(&self) -> PhysPageNum {
        self.start.to_ppn()
    }

    pub fn end_ppn(&self) -> PhysPageNum {
        self.end().to_ppn()
    }

    pub fn page_count(&self) -> usize {
        (self.size + 0xfff) / 0x1000  // Round up to page boundary
    }
}

/// Global memory regions discovered during boot
static mut MEMORY_REGIONS: Vec<MemoryRegion> = Vec::new();

/// Simple device tree parsing for memory regions
/// This is a minimal implementation that looks for memory nodes
pub fn detect_memory_regions(dtb_addr: usize) -> Vec<MemoryRegion> {
    let mut regions = Vec::new();
    
    // For now, we'll use a simple approach that works with QEMU's standard setup
    // In a real implementation, we would parse the full device tree
    
    // QEMU typically provides memory information through OpenSBI
    // We can get this information from the SBI base extension or use hardcoded values
    // that match QEMU's default memory layout
    
    // Default QEMU RISC-V memory layout (this should be replaced with proper DTB parsing)
    // Memory usually starts at 0x80000000 and we need to avoid the kernel area
    
    // For now, let's implement a simple memory detection that reads from
    // well-known locations or uses SBI calls
    
    println!("Detecting memory regions from DTB at 0x{:x}", dtb_addr);
    
    // Simple hardcoded regions for QEMU (this should be replaced with DTB parsing)
    // We'll detect the actual available memory by probing or using SBI
    detect_qemu_memory_regions(&mut regions);
    
    regions
}

/// Detect memory regions in QEMU environment
/// This function implements a simple memory detection for QEMU RISC-V
fn detect_qemu_memory_regions(regions: &mut Vec<MemoryRegion>) {
    // QEMU RISC-V memory layout:
    // - Memory starts at 0x80000000
    // - QEMU was started with -m 1G, so we have 1GB of RAM
    // - Kernel is loaded at 0x80200000 (as shown in OpenSBI output)
    // - We need to find available memory after the kernel
    
    let kernel_end = crate::mem::kernel_end_addr();
    println!("Kernel end virtual address: 0x{:x}", kernel_end);
    
    // RISC-V kernel virtual address space starts at 0xffffffff00000000
    // Physical memory starts at 0x80000000
    // So we need to convert virtual to physical addresses
    let kernel_end_phys = if kernel_end >= 0xffffffff00000000 {
        kernel_end - 0xffffffff00000000 + 0x80000000
    } else {
        kernel_end // Already physical
    };
    
    println!("Kernel end physical address: 0x{:x}", kernel_end_phys);
    
    // Align kernel end to page boundary (4KB)
    let start_addr = PhysAddr::new((kernel_end_phys + 0xfff) & !0xfff);
    
    // QEMU was started with -m 1G, so total memory is 1GB
    let total_memory_size = 1024 * 1024 * 1024; // 1GB
    let memory_base = 0x80000000;
    let memory_end = memory_base + total_memory_size;
    
    println!("Memory base: 0x{:x}, Memory end: 0x{:x}", memory_base, memory_end);
    println!("Available memory start: 0x{:x}", start_addr.as_usize());
    
    if start_addr.as_usize() < memory_end {
        let available_size = memory_end - start_addr.as_usize();
        regions.push(MemoryRegion::new(start_addr, available_size));
        println!("Detected memory region: 0x{:x} - 0x{:x} (size: {} MB)", 
                start_addr.as_usize(), 
                start_addr.as_usize() + available_size,
                available_size / (1024 * 1024));
    } else {
        println!("Warning: No available memory found after kernel!");
        // Fallback: create a small region for testing
        let fallback_start = PhysAddr::new(0x81000000); // 16MB after memory base
        let fallback_size = 64 * 1024 * 1024; // 64MB
        regions.push(MemoryRegion::new(fallback_start, fallback_size));
        println!("Using fallback memory region: 0x{:x} - 0x{:x} (size: {} MB)", 
                fallback_start.as_usize(), 
                fallback_start.as_usize() + fallback_size,
                fallback_size / (1024 * 1024));
    }
}

/// Initialize memory detection and return discovered regions
pub fn init_memory_detection(dtb_addr: usize) -> &'static Vec<MemoryRegion> {
    unsafe {
        let regions_ptr = core::ptr::addr_of_mut!(MEMORY_REGIONS);
        *regions_ptr = detect_memory_regions(dtb_addr);
        &*core::ptr::addr_of!(MEMORY_REGIONS)
    }
}

/// Get the discovered memory regions
pub fn get_memory_regions() -> &'static Vec<MemoryRegion> {
    unsafe { &*core::ptr::addr_of!(MEMORY_REGIONS) }
}

/// Add a memory region manually (for testing or special cases)
pub fn add_memory_region(region: MemoryRegion) {
    unsafe {
        let regions_ptr = core::ptr::addr_of_mut!(MEMORY_REGIONS);
        (*regions_ptr).push(region);
    }
}

/// Clear all memory regions (for testing)
pub fn clear_memory_regions() {
    unsafe {
        let regions_ptr = core::ptr::addr_of_mut!(MEMORY_REGIONS);
        (*regions_ptr).clear();
    }
}

/// Print all discovered memory regions
pub fn print_memory_regions() {
    let regions = get_memory_regions();
    println!("Discovered {} memory regions:", regions.len());
    for (i, region) in regions.iter().enumerate() {
        println!("  Region {}: 0x{:x} - 0x{:x} (size: {} KB, {} pages)",
                i,
                region.start.as_usize(),
                region.end().as_usize(),
                region.size / 1024,
                region.page_count());
    }
}
