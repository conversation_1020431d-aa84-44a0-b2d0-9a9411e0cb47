//! RISC-V SBI (Supervisor Binary Interface) implementation
//! 
//! This module provides an interface to communicate with the SBI implementation
//! (such as OpenSBI) running in M-mode.

use core::arch::asm;

/// SBI call structure containing extension ID and function ID
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct SBICall {
    pub eid: usize, // Extension ID
    pub fid: usize, // Function ID
}

/// SBI return value structure
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct SBIRet {
    pub error: isize,
    pub value: usize,
}

/// SBI error codes
#[allow(dead_code)]
pub mod error {
    pub const SBI_SUCCESS: isize = 0;
    pub const SBI_ERR_FAILED: isize = -1;
    pub const SBI_ERR_NOT_SUPPORTED: isize = -2;
    pub const SBI_ERR_INVALID_PARAM: isize = -3;
    pub const SBI_ERR_DENIED: isize = -4;
    pub const SBI_ERR_INVALID_ADDRESS: isize = -5;
    pub const SBI_ERR_ALREADY_AVAILABLE: isize = -6;
    pub const SBI_ERR_ALREADY_STARTED: isize = -7;
    pub const SBI_ERR_ALREADY_STOPPED: isize = -8;
}

/// SBI extension IDs
#[allow(dead_code)]
pub mod eid {
    pub const SBI_EXT_BASE: usize = 0x10;
    pub const SBI_EXT_TIME: usize = 0x54494D45;
    pub const SBI_EXT_IPI: usize = 0x735049;
    pub const SBI_EXT_RFENCE: usize = 0x52464E43;
    pub const SBI_EXT_HSM: usize = 0x48534D;
    pub const SBI_EXT_SRST: usize = 0x53525354;
    pub const SBI_EXT_PMU: usize = 0x504D55;
    pub const SBI_EXT_DBCN: usize = 0x4442434E;
    
    // Legacy extensions
    pub const SBI_EXT_0_1_SET_TIMER: usize = 0x0;
    pub const SBI_EXT_0_1_CONSOLE_PUTCHAR: usize = 0x1;
    pub const SBI_EXT_0_1_CONSOLE_GETCHAR: usize = 0x2;
    pub const SBI_EXT_0_1_CLEAR_IPI: usize = 0x3;
    pub const SBI_EXT_0_1_SEND_IPI: usize = 0x4;
    pub const SBI_EXT_0_1_REMOTE_FENCE_I: usize = 0x5;
    pub const SBI_EXT_0_1_REMOTE_SFENCE_VMA: usize = 0x6;
    pub const SBI_EXT_0_1_REMOTE_SFENCE_VMA_ASID: usize = 0x7;
    pub const SBI_EXT_0_1_SHUTDOWN: usize = 0x8;
}

/// SBI function IDs for base extension
#[allow(dead_code)]
pub mod base_fid {
    pub const SBI_BASE_GET_SPEC_VERSION: usize = 0x0;
    pub const SBI_BASE_GET_IMPL_ID: usize = 0x1;
    pub const SBI_BASE_GET_IMPL_VERSION: usize = 0x2;
    pub const SBI_BASE_PROBE_EXT: usize = 0x3;
    pub const SBI_BASE_GET_MVENDORID: usize = 0x4;
    pub const SBI_BASE_GET_MARCHID: usize = 0x5;
    pub const SBI_BASE_GET_MIMPID: usize = 0x6;
}

/// Make an SBI call with the given parameters
#[inline(always)]
fn sbi_call(which: SBICall, arg0: usize, arg1: usize, arg2: usize) -> SBIRet {
    let ret1: isize;
    let ret2: usize;
    unsafe {
        asm!(
            "ecall",
            in("x10") arg0,
            in("x11") arg1,
            in("x12") arg2,
            in("x16") which.fid,
            in("x17") which.eid,
            lateout("x10") ret1,
            lateout("x11") ret2,
            options(nostack)
        );
    }
    SBIRet {
        error: ret1,
        value: ret2,
    }
}

/// Console output via SBI
pub fn console_putchar(c: u8) {
    sbi_call(
        SBICall {
            eid: eid::SBI_EXT_0_1_CONSOLE_PUTCHAR,
            fid: 0,
        },
        c as usize,
        0,
        0,
    );
}

/// Console input via SBI
pub fn console_getchar() -> Option<u8> {
    let ret = sbi_call(
        SBICall {
            eid: eid::SBI_EXT_0_1_CONSOLE_GETCHAR,
            fid: 0,
        },
        0,
        0,
        0,
    );
    if ret.error < 0 {
        None
    } else {
        Some(ret.value as u8)
    }
}

/// Shutdown the system via SBI
pub fn shutdown() -> ! {
    sbi_call(
        SBICall {
            eid: eid::SBI_EXT_0_1_SHUTDOWN,
            fid: 0,
        },
        0,
        0,
        0,
    );
    loop {}
}

/// Set timer via SBI
pub fn set_timer(time: u64) {
    sbi_call(
        SBICall {
            eid: eid::SBI_EXT_0_1_SET_TIMER,
            fid: 0,
        },
        time as usize,
        (time >> 32) as usize,
        0,
    );
}

/// Get SBI specification version
pub fn get_spec_version() -> SBIRet {
    sbi_call(
        SBICall {
            eid: eid::SBI_EXT_BASE,
            fid: base_fid::SBI_BASE_GET_SPEC_VERSION,
        },
        0,
        0,
        0,
    )
}

/// Get SBI implementation ID
pub fn get_impl_id() -> SBIRet {
    sbi_call(
        SBICall {
            eid: eid::SBI_EXT_BASE,
            fid: base_fid::SBI_BASE_GET_IMPL_ID,
        },
        0,
        0,
        0,
    )
}

/// Get SBI implementation version
pub fn get_impl_version() -> SBIRet {
    sbi_call(
        SBICall {
            eid: eid::SBI_EXT_BASE,
            fid: base_fid::SBI_BASE_GET_IMPL_VERSION,
        },
        0,
        0,
        0,
    )
}

/// Probe SBI extension
pub fn probe_extension(extension_id: usize) -> SBIRet {
    sbi_call(
        SBICall {
            eid: eid::SBI_EXT_BASE,
            fid: base_fid::SBI_BASE_PROBE_EXT,
        },
        extension_id,
        0,
        0,
    )
}