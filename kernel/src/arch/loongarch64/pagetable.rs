//! LoongArch64 page table implementation
//!
//! This module implements the page table traits for LoongArch64 architecture,
//! providing 4-level page table support.

use crate::mem::address::{PhysAddr, PhysPageNum, VirtAddr, VirtPageNum};
use crate::mem::pagetable::{PageTable, PageTableEntry, PageTableError, PTEFlags, CommonPTEFlags};
use core::fmt::Debug;

/// LoongArch64 page table entry
#[derive(Clone, Copy)]
pub struct La64PageTableEntry {
    pub bits: usize,
}

impl Debug for La64PageTableEntry {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        f.debug_struct("La64PageTableEntry")
            .field("bits", &format_args!("{:#x}", self.bits))
            .field("valid", &self.is_valid())
            .field("readable", &self.is_readable())
            .field("writable", &self.is_writable())
            .field("executable", &self.is_executable())
            .finish()
    }
}

impl La64PageTableEntry {
    /// Create a new empty PTE
    pub fn new() -> Self {
        Self { bits: 0 }
    }
    
    /// Create PTE from raw bits
    pub fn from_bits(bits: usize) -> Self {
        Self { bits }
    }
}

impl PageTableEntry for La64PageTableEntry {
    fn new() -> Self {
        Self { bits: 0 }
    }
    
    fn new_page(ppn: PhysPageNum, flags: PTEFlags) -> Self {
        Self {
            // LoongArch64 PTE format: [63:12] PPN, [11:0] flags
            bits: (ppn.0 << 12) | flags.bits(),
        }
    }
    
    fn ppn(&self) -> PhysPageNum {
        PhysPageNum(self.bits >> 12)
    }
    
    fn flags(&self) -> PTEFlags {
        PTEFlags::from_bits(self.bits & 0xfff)
    }
    
    fn is_valid(&self) -> bool {
        (self.bits & 0x1) != 0
    }
    
    fn is_readable(&self) -> bool {
        (self.bits & 0x2) != 0
    }
    
    fn is_writable(&self) -> bool {
        (self.bits & 0x4) != 0
    }
    
    fn is_executable(&self) -> bool {
        (self.bits & 0x8) != 0
    }
    
    fn set_flags(&mut self, flags: PTEFlags) {
        self.bits = (self.bits & !0xfff) | flags.bits();
    }
    
    fn clear(&mut self) {
        self.bits = 0;
    }
}

/// LoongArch64 4-level page table
pub struct La64PageTable {
    root_ppn: PhysPageNum,
    entries: &'static mut [La64PageTableEntry],
}

impl Debug for La64PageTable {
    fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
        f.debug_struct("La64PageTable")
            .field("root_ppn", &self.root_ppn)
            .field("entries_len", &self.entries.len())
            .finish()
    }
}

impl La64PageTable {
    /// Create a new page table with given root page
    pub fn from_root(root_ppn: PhysPageNum) -> Self {
        let entries = unsafe {
            core::slice::from_raw_parts_mut(
                root_ppn.into_pa().0 as *mut La64PageTableEntry,
                512, // 512 entries per page table
            )
        };
        Self { root_ppn, entries }
    }
    
    /// Get page table entry at given index
    pub fn get_entry(&self, index: usize) -> Option<&La64PageTableEntry> {
        self.entries.get(index)
    }
    
    /// Get mutable page table entry at given index
    pub fn get_entry_mut(&mut self, index: usize) -> Option<&mut La64PageTableEntry> {
        self.entries.get_mut(index)
    }
    
    /// Walk the page table to find the PTE for given VPN
    fn walk(&self, vpn: VirtPageNum) -> Result<&La64PageTableEntry, PageTableError> {
        let vpn_bits = vpn.0;
        let level3_idx = (vpn_bits >> 27) & 0x1ff; // bits [35:27]
        let level2_idx = (vpn_bits >> 18) & 0x1ff; // bits [26:18]
        let level1_idx = (vpn_bits >> 9) & 0x1ff;  // bits [17:9]
        let level0_idx = vpn_bits & 0x1ff;         // bits [8:0]
        
        // Level 3 (root level)
        let pte3 = &self.entries[level3_idx];
        if !pte3.is_valid() {
            return Err(PageTableError::NotMapped);
        }
        
        // Check if this is a leaf PTE (huge page)
        if pte3.is_readable() || pte3.is_writable() || pte3.is_executable() {
            return Ok(pte3);
        }
        
        // Level 2
        let level2_table = unsafe {
            core::slice::from_raw_parts(
                pte3.ppn().into_pa().0 as *const La64PageTableEntry,
                512,
            )
        };
        let pte2 = &level2_table[level2_idx];
        if !pte2.is_valid() {
            return Err(PageTableError::NotMapped);
        }
        
        // Check if this is a leaf PTE (large page)
        if pte2.is_readable() || pte2.is_writable() || pte2.is_executable() {
            return Ok(pte2);
        }
        
        // Level 1
        let level1_table = unsafe {
            core::slice::from_raw_parts(
                pte2.ppn().into_pa().0 as *const La64PageTableEntry,
                512,
            )
        };
        let pte1 = &level1_table[level1_idx];
        if !pte1.is_valid() {
            return Err(PageTableError::NotMapped);
        }
        
        // Check if this is a leaf PTE (medium page)
        if pte1.is_readable() || pte1.is_writable() || pte1.is_executable() {
            return Ok(pte1);
        }
        
        // Level 0
        let level0_table = unsafe {
            core::slice::from_raw_parts(
                pte1.ppn().into_pa().0 as *const La64PageTableEntry,
                512,
            )
        };
        let pte0 = &level0_table[level0_idx];
        if !pte0.is_valid() {
            return Err(PageTableError::NotMapped);
        }
        
        Ok(pte0)
    }
}

impl PageTable for La64PageTable {
    type Entry = La64PageTableEntry;
    
    fn new() -> Self {
        // For now, create a dummy page table
        // In a real implementation, this would allocate a new page
        let dummy_ppn = PhysPageNum(0);
        let entries = unsafe {
            core::slice::from_raw_parts_mut(
                0x2000 as *mut La64PageTableEntry, // Dummy address
                512,
            )
        };
        Self { root_ppn: dummy_ppn, entries }
    }
    
    fn map(&mut self, vpn: VirtPageNum, ppn: PhysPageNum, flags: PTEFlags) -> Result<(), PageTableError> {
        // Simplified mapping - in a real implementation, this would handle
        // multi-level page table creation and mapping
        let vpn_bits = vpn.0;
        let index = vpn_bits & 0x1ff; // Use lower 9 bits as index for simplicity
        
        if index >= self.entries.len() {
            return Err(PageTableError::InvalidAddress);
        }
        
        let entry = &mut self.entries[index];
        *entry = La64PageTableEntry::new_page(ppn, flags);
        
        Ok(())
    }
    
    fn unmap(&mut self, vpn: VirtPageNum) -> Result<(), PageTableError> {
        let vpn_bits = vpn.0;
        let index = vpn_bits & 0x1ff; // Use lower 9 bits as index for simplicity
        
        if index >= self.entries.len() {
            return Err(PageTableError::InvalidAddress);
        }
        
        let entry = &mut self.entries[index];
        entry.clear();
        
        Ok(())
    }
    
    fn translate(&self, vpn: VirtPageNum) -> Option<PhysPageNum> {
        match self.walk(vpn) {
            Ok(pte) => {
                if pte.is_valid() {
                    Some(pte.ppn())
                } else {
                    None
                }
            }
            Err(_) => None,
        }
    }
    
    fn flush_tlb(&self, va: VirtAddr) {
        unsafe {
            core::arch::asm!("invtlb 0x6, {}, {}", in(reg) 0, in(reg) va.0);
        }
    }
    
    fn flush_tlb_all(&self) {
        unsafe {
            core::arch::asm!("tlbclr");
        }
    }
    
    fn from_token(token: usize) -> Self {
        let ppn = PhysPageNum::from(token);
        Self {
            entries: unsafe { core::slice::from_raw_parts_mut(ppn.into_pa().0 as *mut La64PageTableEntry, 512) },
            root_ppn: ppn,
        }
    }
    
    fn token(&self) -> usize {
        let ppn = PhysPageNum::from(self.entries.as_ptr() as usize >> 12);
        ppn.0
    }
    
    fn find_pte_create(&mut self, vpn: VirtPageNum) -> Result<&mut Self::Entry, PageTableError> {
        let vpn_bits = vpn.0;
        let index = vpn_bits & 0x1ff; // Use lower 9 bits as index for simplicity
        
        if index >= self.entries.len() {
            return Err(PageTableError::InvalidAddress);
        }
        
        Ok(&mut self.entries[index])
    }
    
    fn find_pte(&self, vpn: VirtPageNum) -> Option<&Self::Entry> {
        let vpn_bits = vpn.0;
        let index = vpn_bits & 0x1ff; // Use lower 9 bits as index for simplicity
        
        if index >= self.entries.len() {
            return None;
        }
        
        let entry = &self.entries[index];
        if entry.is_valid() {
            Some(entry)
        } else {
            None
        }
    }
    
    fn find_pte_mut(&mut self, vpn: VirtPageNum) -> Option<&mut Self::Entry> {
        let vpn_bits = vpn.0;
        let index = vpn_bits & 0x1ff; // Use lower 9 bits as index for simplicity
        
        if index >= self.entries.len() {
            return None;
        }
        
        let entry = &mut self.entries[index];
        if entry.is_valid() {
            Some(entry)
        } else {
            None
        }
    }
    
    fn activate(&self) {
        let ppn = PhysPageNum::from(self.entries.as_ptr() as usize >> 12);
        unsafe {
            core::arch::asm!("csrwr {}, 0x19", in(reg) ppn.0 << 12); // PGDL register
        }
    }
}

/// LoongArch64 specific PTE flags
pub struct La64PTEFlags;

impl La64PTEFlags {
    /// Valid flag
    pub const VALID: PTEFlags = PTEFlags::from_bits(1 << 0);
    /// Readable flag  
    pub const READABLE: PTEFlags = PTEFlags::from_bits(1 << 1);
    /// Writable flag
    pub const WRITABLE: PTEFlags = PTEFlags::from_bits(1 << 2);
    /// Executable flag
    pub const EXECUTABLE: PTEFlags = PTEFlags::from_bits(1 << 3);
    /// User accessible flag
    pub const USER: PTEFlags = PTEFlags::from_bits(1 << 4);
    /// Global flag
    pub const GLOBAL: PTEFlags = PTEFlags::from_bits(1 << 5);
    /// Accessed flag
    pub const ACCESSED: PTEFlags = PTEFlags::from_bits(1 << 6);
    /// Dirty flag
    pub const DIRTY: PTEFlags = PTEFlags::from_bits(1 << 7);
    /// Present in Level 1 flag (LoongArch64 specific)
    pub const PLV1: PTEFlags = PTEFlags::from_bits(1 << 8);
    /// Present in Level 2 flag (LoongArch64 specific)
    pub const PLV2: PTEFlags = PTEFlags::from_bits(1 << 9);
    /// Present in Level 3 flag (LoongArch64 specific)
    pub const PLV3: PTEFlags = PTEFlags::from_bits(1 << 10);
    /// Non-readable flag (LoongArch64 specific)
    pub const NR: PTEFlags = PTEFlags::from_bits(1 << 11);
}
