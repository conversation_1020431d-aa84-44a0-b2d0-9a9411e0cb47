use ns16550a::Uart;
use spin::Mutex;


pub struct DebugConsole;
const UART_BASE: usize = 0x1FE001E0;
const UART_VIRT_BASE: usize = UART_BASE | crate::arch::consts::VIRT_ADDR_START;

static COM1: Mutex<Uart> = Mutex::new(Uart::new(UART_VIRT_BASE));

impl DebugConsole {
    /// Ensures UART instance is ready (no explicit configuration needed in QEMU).
    pub fn init() {
        // Simply touch the UART to ensure the Mutex is initialized.
        let _ = COM1.lock();
    }
    /// Writes a byte to the console.
    #[inline]
    pub fn putchar(ch: u8) {
        if ch == b'\n' {
            COM1.lock().put(b'\r');
        }
        COM1.lock().put(ch);
    }

    /// read a byte, return -1 if nothing exists.
    #[inline]
    pub fn getchar() -> Option<u8> {
        COM1.lock().get()
    }
}