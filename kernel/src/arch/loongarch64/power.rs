use super::consts::VIRT_ADDR_START;
use crate::println;
use core::arch::asm;

/// Shutdown the system using LoongArch64 QEMU virt machine power management
#[inline]
pub fn shutdown() -> ! {
    println!("Shutting down...");
    
    // Write to GED (Generic Event Device) for ACPI power management
    unsafe {
        let ged_addr = (0x100E001C | VIRT_ADDR_START) as *mut u8;
        core::ptr::write_volatile(ged_addr, 0x34); // S5 power off
    }
    
    // Idle until system powers off
    loop {
        unsafe { asm!("idle 0", options(nomem, nostack)); }
    }
}