//! LoongArch64 I/O implementation
//!
//! Simple console I/O for LoongArch64 architecture

use core::fmt::{self, Write};

/// Console writer structure
pub struct Console;
use super::uart::DebugConsole;
impl Console {
    /// Create a new console instance
    pub const fn new() -> Self {
        Console
    }

    /// Write a single character to the console
    pub fn putchar(&self, c: u8) {
        // Use the UART driver for LoongArch64
        DebugConsole::putchar(c);
    }

    /// Write a string to the console
    pub fn puts(&self, s: &str) {
        for byte in s.bytes() {
            self.putchar(byte);
        }
    }

    /// Read a character from the console (if available)
    pub fn getchar(&self) -> Option<u8> {
        // TODO: Implement actual UART input for LoongArch64
        None
    }
}

/// Implement the Write trait for Console to enable format! macros
impl Write for Console {
    fn write_str(&mut self, s: &str) -> fmt::Result {
        self.puts(s);
        Ok(())
    }
}

static mut CONSOLE: Console = Console::new();

/// Get a mutable reference to the global console
pub fn console() -> &'static mut Console {
    unsafe { &mut *(&raw mut CONSOLE) }
}

/// Print macro for LoongArch64
#[macro_export]
macro_rules! print {
    ($($arg:tt)*) => {
        $crate::arch::loongarch64::io::_print(format_args!($($arg)*));
    };
}

/// Println macro for LoongArch64
#[macro_export]
macro_rules! println {
    () => {
        $crate::print!("\n");
    };
    ($($arg:tt)*) => {
        $crate::print!("{}", format_args!($($arg)*));
        $crate::print!("\n");
    };
}

/// Print function that writes to the console
pub fn _print(args: fmt::Arguments) {
    console().write_fmt(args).unwrap();
}

/// Debug print macro - only prints in debug builds
#[macro_export]
macro_rules! dbg_print {
    ($($arg:tt)*) => {
        #[cfg(debug_assertions)]
        $crate::print!($($arg)*);
    };
}

/// Debug println macro - only prints in debug builds
#[macro_export]
macro_rules! dbg_println {
    () => {
        #[cfg(debug_assertions)]
        $crate::println!();
    };
    ($($arg:tt)*) => {
        #[cfg(debug_assertions)]
        $crate::println!($($arg)*);
    };
}
