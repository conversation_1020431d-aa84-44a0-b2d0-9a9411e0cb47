//! Architecture-specific code
//!
//! This module provides a unified interface to architecture-specific functionality
//! for different target architectures.

#[cfg(target_arch = "riscv64")]
pub mod riscv64;

#[cfg(target_arch = "loongarch64")]
pub mod loongarch64;

// Re-export architecture-specific items
#[cfg(target_arch = "riscv64")]
pub use riscv64::*;

#[cfg(target_arch = "loongarch64")]
pub use loongarch64::*;