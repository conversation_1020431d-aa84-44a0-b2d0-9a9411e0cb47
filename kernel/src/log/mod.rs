//! Logging utilities for the kernel.
pub mod color;
pub mod switch;
#[macro_export]
macro_rules! log {
    ($level:expr, $color:expr, $($arg:tt)*) => ({
        #[cfg(target_arch = "riscv64")]
        {
            $crate::arch::riscv64::io::_print(format_args!("{}[{:<5}] ", $color, $level));
            $crate::arch::riscv64::io::_print(format_args!($($arg)*));
            $crate::arch::riscv64::io::_print(format_args!("{}\n", $crate::log::color::RESET));
        }

        #[cfg(target_arch = "loongarch64")]
        {
            $crate::arch::loongarch64::io::_print(format_args!("{}[{:<5}] ", $color, $level));
            $crate::arch::loongarch64::io::_print(format_args!($($arg)*));
            $crate::arch::loongarch64::io::_print(format_args!("{}\n", $crate::log::color::RESET));
        }
    });
}

#[macro_export]
macro_rules! info {
    ($($arg:tt)*) => ($crate::log!("INFO", $crate::log::color::GREEN, $($arg)*));
}

#[macro_export]
macro_rules! warn {
    ($($arg:tt)*) => ($crate::log!("WARN", $crate::log::color::YELLOW, $($arg)*));
}

#[macro_export]
macro_rules! error {
    ($($arg:tt)*) => {
        if crate::log::switch::ERROR_LOG {
            $crate::log!("ERROR", $crate::log::color::RED, $($arg)*);
        }
    };
}

#[macro_export]
macro_rules! debug {
    ($($arg:tt)*) => ($crate::log!("DEBUG", $crate::log::color::CYAN, $($arg)*));
}

#[macro_export]
macro_rules! trace {
    ($($arg:tt)*) => ($crate::log!("TRACE", $crate::log::color::MAGENTA, $($arg)*));
}

#[macro_export]
macro_rules! syscall_log {
    ($($arg:tt)*) => {
        if crate::log::switch::SYSCALL_LOG {
            $crate::log!("SYSCALL", $crate::log::color::BLUE, $($arg)*);
        }
    };
}

#[macro_export]
macro_rules! pagetable_log {
    ($($arg:tt)*) => {
        if crate::log::switch::PAGETABLE_LOG {
            $crate::log!("PAGETABLE", $crate::log::color::BLUE, $($arg)*);
        }
    };
}

#[macro_export]
macro_rules! dtblog {
    ($($arg:tt)*) => {
        if crate::log::switch::DTB_LOG {
            $crate::log!("DTB", $crate::log::color::GREEN, $($arg)*);
        }
    };
}

/// Frame allocator logging macro
#[macro_export]
macro_rules! framelog {
    ($($arg:tt)*) => {
        if crate::log::switch::FRAME_LOG {
            $crate::log!("FRAME", $crate::log::color::BLUE, $($arg)*);
        }
    };
}
