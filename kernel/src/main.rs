#![no_std]
#![no_main]

use core::panic::PanicInfo;

mod arch;
mod boot;
mod log;
mod mem;
#[unsafe(no_mangle)]
pub extern "C" fn rust_main() -> ! {
    // Initialize architecture-specific hardware (UART, etc.)
    println!("Hello from LoongArch64 kernel!");
    info!("Hello from LoongArch64 kernel!");
    
    // Initialize frame allocator
    println!("\nInitializing frame allocator...");
    
    #[cfg(target_arch = "riscv64")]
    unsafe {
        // RISC-V64: Use tested memory layout
        let start_addr = mem::address::PhysAddr::new(0x8020_0000); // RISC-V64: After kernel
        let end_addr = mem::address::PhysAddr::new(0x8800_0000);   // 8MB range
        let bitmap_addr = mem::address::PhysAddr::new(0x8010_0000); // Before kernel data
        
        mem::frame::init_frame_allocator(start_addr, end_addr, bitmap_addr);
        println!("[PASS] Frame allocator initialization successful");
        info!("Frame allocator initialization successful for RISC-V64");
    }
    
    #[cfg(target_arch = "loongarch64")]
    unsafe {
        // LoongArch64 DMW (Direct Mapping Windows) configuration:
        // DMW0: 0x8000000000000000-0x8FFFFFFFFFFFFFFF -> 0x0000000000000000-0x0FFFFFFFFFFFFFFF
        // DMW1: 0x9000000000000000-0x9FFFFFFFFFFFFFFF -> 0x0000000000000000-0x0FFFFFFFFFFFFFFF
        // Use DMW0 mapped addresses for frame allocator
        let start_addr = mem::address::PhysAddr::new(0x8000_0000_0200_0000); // DMW0 + 32MB offset
        let end_addr = mem::address::PhysAddr::new(0x8000_0000_0800_0000);   // DMW0 + 128MB
        let bitmap_addr = mem::address::PhysAddr::new(0x8000_0000_0100_0000); // DMW0 + 16MB for bitmap
        
        mem::frame::init_frame_allocator(start_addr, end_addr, bitmap_addr);
        println!("[PASS] Frame allocator initialization successful");
        info!("Frame allocator initialization successful for LoongArch64");
    }
    
    // Initialize heap memory
    println!("\nInitializing heap memory...");
    unsafe {
        if let Err(e) = mem::heap::init_heap() {
            println!("[FAIL] Heap initialization failed: {}", e);
            error!("Heap initialization failed: {}", e);
        } else {
            println!("[PASS] Heap initialization successful");
            info!("Heap initialization successful");
            mem::heap::print_heap_info();
        }
    }
    
    // Run memory management tests
    // Run comprehensive memory management test suite
    let (passed, total) = mem::tests::run_all_tests();
    
    if passed == total {
        info!("All memory management tests passed successfully");
    } else {
        error!("Some memory management tests failed: {}/{}", passed, total);
    }
    
    println!("\nKernel initialization complete.");
    info!("Kernel initialization complete");
    
    // Shutdown the system gracefully
    println!("\nShutting down kernel...");
    info!("Kernel shutdown initiated");
    arch::shutdown();
}


#[panic_handler]
fn panic(info: &PanicInfo) -> ! {
    error!("Kernel panic occurred!");
    if let Some(location) = info.location() {
        error!("Location: {}:{}", location.file(), location.line());
    }
    error!("Message: {}", info.message());
    
    error!("System halted.");
    loop {}
}
