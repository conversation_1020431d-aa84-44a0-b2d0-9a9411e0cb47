use std::{env, fs};
use std::path::PathBuf;

fn main() {
    println!("cargo:rerun-if-env-changed=LOG");
    println!("cargo:rerun-if-env-changed=SMP");
    println!("cargo:rerun-if-env-changed=BOARD");
    println!("cargo:rerun-if-env-changed=USER_IMG");

    let _arch: String = env::var("ARCH").unwrap_or_default();
    if let Ok(user_img) = env::var("USER_IMG") {
        println!("cargo:rerun-if-changed={}", user_img);
    }

    // for shorter #[cfg] check
    let target = env::var("TARGET").unwrap();
    if target.contains("riscv32") {
        println!("cargo:rustc-cfg=riscv");
        println!("cargo:rustc-cfg=riscv32");
    } else if target.contains("riscv64") {
        println!("cargo:rustc-cfg=riscv");
        println!("cargo:rustc-cfg=riscv64");

        // --- custom linker script integration ---
        let linker_script_src = PathBuf::from("src/boot/riscv64/linker64.ld");
        println!("cargo:rerun-if-changed={}", linker_script_src.display());

        let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());
        let linker_script_dst = out_dir.join("linker64.ld");
        fs::copy(&linker_script_src, &linker_script_dst)
            .expect("Failed to copy linker script");

        println!("cargo:rustc-link-arg=-T{}", linker_script_dst.display());
        // -----------------------------------------
    } else if target.contains("loongarch64") {
        println!("cargo:rustc-cfg=loongarch64");

        // Disable vector instructions for LoongArch64
        println!("cargo:rustc-codegen-arg=-C");
        println!("cargo:rustc-codegen-arg=target-feature=-lsx,-lasx");
        println!("cargo:rustc-codegen-arg=-C");
        println!("cargo:rustc-codegen-arg=target-cpu=generic");

        // --- custom linker script integration for LoongArch64 ---
        let linker_script_src = PathBuf::from("src/boot/loongarch64/linker64.ld");
        println!("cargo:rerun-if-changed={}", linker_script_src.display());

        let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());
        let linker_script_dst = out_dir.join("linker64.ld");
        fs::copy(&linker_script_src, &linker_script_dst)
            .expect("Failed to copy LoongArch64 linker script");

        println!("cargo:rustc-link-arg=-T{}", linker_script_dst.display());
        // --------------------------------------------------------
    } else if target.contains("mipsel") {
        println!("cargo:rustc-cfg=mipsel");
    } else if target.contains("aarch64") {
        println!("cargo:rustc-cfg=aarch64");
    } else if target.contains("x86_64") {
        println!("cargo:rustc-cfg=x86_64");
    }
}
