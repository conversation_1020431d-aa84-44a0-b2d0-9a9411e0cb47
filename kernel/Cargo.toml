[package]
name = "kernel"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "kernel"
path = "src/main.rs"

[lib]
name = "kernel"
path = "src/lib.rs"

[dependencies]
spin = { workspace = true }
buddy_system_allocator = "0.11.0"

[target.loongarch64-unknown-none.dependencies]
ns16550a = "0.5.0"
loongArch64 = "0.2.5"


# Disable vector instructions for LoongArch64
[target.loongarch64-unknown-none]
rustflags = [
    "-C", "target-feature=-lsx,-lasx"
]